@page "/plant-sector/crops"
@using MudBlazor

<PageTitle>إدارة المحاصيل - القطاع النباتي</PageTitle>

<MudContainer MaxWidth="MaxWidth.ExtraLarge" Class="pa-4">
    <!-- العنوان والأزرار -->
    <MudPaper Class="pa-4 mb-4" Elevation="3">
        <div class="d-flex align-center justify-space-between">
            <div class="d-flex align-center">
                <MudIcon Icon="@Icons.Material.Filled.Grass" Size="Size.Large" Color="Color.Success" Class="me-3" />
                <div>
                    <MudText Typo="Typo.h4" Color="Color.Success" Class="fw-bold">إدارة المحاصيل</MudText>
                    <MudText Typo="Typo.subtitle1">تسجيل ومتابعة جميع المحاصيل الزراعية</MudText>
                </div>
            </div>
            <MudButton Variant="Variant.Filled" Color="Color.Success" StartIcon="@Icons.Material.Filled.Add" OnClick="@OpenAddDialog">
                إضافة محصول جديد
            </MudButton>
        </div>
    </MudPaper>

    <!-- فلاتر البحث -->
    <MudPaper Class="pa-4 mb-4" Elevation="2">
        <MudGrid AlignItems="Center">
            <MudItem xs="12" sm="6" md="3">
                <MudTextField @bind-Value="_searchText" Label="البحث" Placeholder="اسم المحصول أو النوع"
                             Adornment="Adornment.Start" AdornmentIcon="@Icons.Material.Filled.Search"
                             Immediate="true" OnKeyUp="@SearchCrops" />
            </MudItem>
            <MudItem xs="12" sm="6" md="3">
                <MudSelect @bind-Value="_selectedType" Label="نوع المحصول" Clearable="true" OnSelectionChanged="@FilterByType">
                    <MudSelectItem Value="@string.Empty">جميع الأنواع</MudSelectItem>
                    <MudSelectItem Value="خضروات">خضروات</MudSelectItem>
                    <MudSelectItem Value="فواكه">فواكه</MudSelectItem>
                    <MudSelectItem Value="حبوب">حبوب</MudSelectItem>
                    <MudSelectItem Value="بقوليات">بقوليات</MudSelectItem>
                    <MudSelectItem Value="أعلاف">أعلاف</MudSelectItem>
                </MudSelect>
            </MudItem>
            <MudItem xs="12" sm="6" md="3">
                <MudSelect @bind-Value="_selectedStatus" Label="الحالة" Clearable="true" OnSelectionChanged="@FilterByStatus">
                    <MudSelectItem Value="@string.Empty">جميع الحالات</MudSelectItem>
                    <MudSelectItem Value="زراعة">زراعة</MudSelectItem>
                    <MudSelectItem Value="نمو">نمو</MudSelectItem>
                    <MudSelectItem Value="إزهار">إزهار</MudSelectItem>
                    <MudSelectItem Value="نضج">نضج</MudSelectItem>
                    <MudSelectItem Value="حصاد">حصاد</MudSelectItem>
                    <MudSelectItem Value="مكتمل">مكتمل</MudSelectItem>
                </MudSelect>
            </MudItem>
            <MudItem xs="12" sm="6" md="3">
                <MudButton Variant="Variant.Outlined" Color="Color.Primary" FullWidth="true" 
                          StartIcon="@Icons.Material.Filled.Refresh" OnClick="@ResetFilters">
                    إعادة تعيين
                </MudButton>
            </MudItem>
        </MudGrid>
    </MudPaper>

    <!-- جدول المحاصيل -->
    <MudPaper Class="pa-4" Elevation="3">
        <MudTable Items="_filteredCrops" Hover="true" Striped="true" Dense="false" Loading="_loading"
                  FixedHeader="true" Height="600px">
            <ToolBarContent>
                <MudText Typo="Typo.h6">قائمة المحاصيل (@_filteredCrops.Count)</MudText>
                <MudSpacer />
                <MudButton Variant="Variant.Text" Color="Color.Primary" StartIcon="@Icons.Material.Filled.Download">
                    تصدير Excel
                </MudButton>
            </ToolBarContent>
            <HeaderContent>
                <MudTh><MudTableSortLabel SortBy="new Func<Crop, object>(x => x.Name)">اسم المحصول</MudTableSortLabel></MudTh>
                <MudTh><MudTableSortLabel SortBy="new Func<Crop, object>(x => x.Type)">النوع</MudTableSortLabel></MudTh>
                <MudTh><MudTableSortLabel SortBy="new Func<Crop, object>(x => x.Variety)">الصنف</MudTableSortLabel></MudTh>
                <MudTh><MudTableSortLabel SortBy="new Func<Crop, object>(x => x.Area)">المساحة (هكتار)</MudTableSortLabel></MudTh>
                <MudTh><MudTableSortLabel SortBy="new Func<Crop, object>(x => x.PlantingDate)">تاريخ الزراعة</MudTableSortLabel></MudTh>
                <MudTh><MudTableSortLabel SortBy="new Func<Crop, object>(x => x.ExpectedHarvestDate)">تاريخ الحصاد المتوقع</MudTableSortLabel></MudTh>
                <MudTh><MudTableSortLabel SortBy="new Func<Crop, object>(x => x.Status)">الحالة</MudTableSortLabel></MudTh>
                <MudTh>الإجراءات</MudTh>
            </HeaderContent>
            <RowTemplate>
                <MudTd DataLabel="اسم المحصول">
                    <div class="d-flex align-center">
                        <MudAvatar Color="Color.Success" Size="Size.Small" Class="me-2">
                            <MudIcon Icon="@GetCropIcon(context.Type)" />
                        </MudAvatar>
                        <div>
                            <MudText Typo="Typo.body1" Class="fw-bold">@context.Name</MudText>
                            <MudText Typo="Typo.caption" Color="Color.Secondary">@context.FarmName</MudText>
                        </div>
                    </div>
                </MudTd>
                <MudTd DataLabel="النوع">
                    <MudChip Color="@GetTypeColor(context.Type)" Size="Size.Small">@context.Type</MudChip>
                </MudTd>
                <MudTd DataLabel="الصنف">@context.Variety</MudTd>
                <MudTd DataLabel="المساحة">@context.Area.ToString("F1")</MudTd>
                <MudTd DataLabel="تاريخ الزراعة">@context.PlantingDate.ToString("yyyy/MM/dd")</MudTd>
                <MudTd DataLabel="تاريخ الحصاد المتوقع">@context.ExpectedHarvestDate.ToString("yyyy/MM/dd")</MudTd>
                <MudTd DataLabel="الحالة">
                    <MudChip Color="@GetStatusColor(context.Status)" Size="Size.Small">@context.Status</MudChip>
                </MudTd>
                <MudTd DataLabel="الإجراءات">
                    <MudButtonGroup Size="Size.Small" Variant="Variant.Text">
                        <MudIconButton Icon="@Icons.Material.Filled.Visibility" Color="Color.Info" 
                                      OnClick="@(() => ViewCrop(context))" Title="عرض التفاصيل" />
                        <MudIconButton Icon="@Icons.Material.Filled.Edit" Color="Color.Warning" 
                                      OnClick="@(() => EditCrop(context))" Title="تعديل" />
                        <MudIconButton Icon="@Icons.Material.Filled.Timeline" Color="Color.Success" 
                                      OnClick="@(() => ViewProgress(context))" Title="متابعة التقدم" />
                        <MudIconButton Icon="@Icons.Material.Filled.Delete" Color="Color.Error" 
                                      OnClick="@(() => DeleteCrop(context))" Title="حذف" />
                    </MudButtonGroup>
                </MudTd>
            </RowTemplate>
            <PagerContent>
                <MudTablePager PageSizeOptions="new int[]{10, 25, 50, 100}" />
            </PagerContent>
        </MudTable>
    </MudPaper>
</MudContainer>

<!-- حوار إضافة/تعديل المحصول -->
<MudDialog @bind-IsVisible="_showAddEditDialog" Options="_dialogOptions">
    <TitleContent>
        <div class="d-flex align-center">
            <MudIcon Icon="@Icons.Material.Filled.Grass" Class="me-3" />
            <MudText Typo="Typo.h6">@(_isEditing ? "تعديل المحصول" : "إضافة محصول جديد")</MudText>
        </div>
    </TitleContent>
    <DialogContent>
        <MudForm @ref="_form" Model="_currentCrop">
            <MudGrid>
                <MudItem xs="12" md="6">
                    <MudTextField @bind-Value="_currentCrop.Name" Label="اسم المحصول" Required="true" />
                </MudItem>
                <MudItem xs="12" md="6">
                    <MudSelect @bind-Value="_currentCrop.Type" Label="نوع المحصول" Required="true">
                        <MudSelectItem Value="خضروات">خضروات</MudSelectItem>
                        <MudSelectItem Value="فواكه">فواكه</MudSelectItem>
                        <MudSelectItem Value="حبوب">حبوب</MudSelectItem>
                        <MudSelectItem Value="بقوليات">بقوليات</MudSelectItem>
                        <MudSelectItem Value="أعلاف">أعلاف</MudSelectItem>
                    </MudSelect>
                </MudItem>
                <MudItem xs="12" md="6">
                    <MudTextField @bind-Value="_currentCrop.Variety" Label="الصنف" />
                </MudItem>
                <MudItem xs="12" md="6">
                    <MudTextField @bind-Value="_currentCrop.FarmName" Label="اسم المزرعة" Required="true" />
                </MudItem>
                <MudItem xs="12" md="6">
                    <MudNumericField @bind-Value="_currentCrop.Area" Label="المساحة (هكتار)" Required="true" Min="0" />
                </MudItem>
                <MudItem xs="12" md="6">
                    <MudDatePicker @bind-Date="_currentCrop.PlantingDate" Label="تاريخ الزراعة" Required="true" />
                </MudItem>
                <MudItem xs="12" md="6">
                    <MudDatePicker @bind-Date="_currentCrop.ExpectedHarvestDate" Label="تاريخ الحصاد المتوقع" />
                </MudItem>
                <MudItem xs="12" md="6">
                    <MudSelect @bind-Value="_currentCrop.Status" Label="الحالة" Required="true">
                        <MudSelectItem Value="زراعة">زراعة</MudSelectItem>
                        <MudSelectItem Value="نمو">نمو</MudSelectItem>
                        <MudSelectItem Value="إزهار">إزهار</MudSelectItem>
                        <MudSelectItem Value="نضج">نضج</MudSelectItem>
                        <MudSelectItem Value="حصاد">حصاد</MudSelectItem>
                        <MudSelectItem Value="مكتمل">مكتمل</MudSelectItem>
                    </MudSelect>
                </MudItem>
                <MudItem xs="12">
                    <MudTextField @bind-Value="_currentCrop.Notes" Label="ملاحظات" Lines="3" />
                </MudItem>
            </MudGrid>
        </MudForm>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="@CloseDialog">إلغاء</MudButton>
        <MudButton Color="Color.Success" Variant="Variant.Filled" OnClick="@SaveCrop">
            @(_isEditing ? "تحديث" : "حفظ")
        </MudButton>
    </DialogActions>
</MudDialog>

@code {
    // المتغيرات
    private bool _loading = false;
    private string _searchText = string.Empty;
    private string _selectedType = string.Empty;
    private string _selectedStatus = string.Empty;

    // البيانات
    private List<Crop> _crops = new();
    private List<Crop> _filteredCrops = new();

    // الحوار
    private bool _showAddEditDialog = false;
    private bool _isEditing = false;
    private Crop _currentCrop = new();
    private MudForm _form = null!;

    private readonly DialogOptions _dialogOptions = new()
    {
        MaxWidth = MaxWidth.Medium,
        FullWidth = true,
        CloseButton = true,
        DisableBackdropClick = true
    };

    protected override async Task OnInitializedAsync()
    {
        await LoadCrops();
    }

    private async Task LoadCrops()
    {
        _loading = true;
        
        // محاكاة تحميل البيانات
        await Task.Delay(1000);
        
        _crops = new List<Crop>
        {
            new() { Id = 1, Name = "طماطم الصوب", Type = "خضروات", Variety = "هجين F1", FarmName = "مزرعة الخير", Area = 25.5m, PlantingDate = DateTime.Now.AddDays(-30), ExpectedHarvestDate = DateTime.Now.AddDays(60), Status = "نمو", Notes = "نمو جيد" },
            new() { Id = 2, Name = "خيار أخضر", Type = "خضروات", Variety = "محلي", FarmName = "مزرعة النماء", Area = 18.2m, PlantingDate = DateTime.Now.AddDays(-25), ExpectedHarvestDate = DateTime.Now.AddDays(45), Status = "إزهار", Notes = "إزهار ممتاز" },
            new() { Id = 3, Name = "باذنجان أسود", Type = "خضروات", Variety = "بلدي", FarmName = "مزرعة الأمل", Area = 22.8m, PlantingDate = DateTime.Now.AddDays(-40), ExpectedHarvestDate = DateTime.Now.AddDays(30), Status = "نضج", Notes = "جاهز للحصاد قريباً" },
            new() { Id = 4, Name = "تمر خلاص", Type = "فواكه", Variety = "خلاص", FarmName = "مزرعة النخيل", Area = 45.0m, PlantingDate = DateTime.Now.AddDays(-1825), ExpectedHarvestDate = DateTime.Now.AddDays(120), Status = "نضج", Notes = "موسم التمر" },
            new() { Id = 5, Name = "برسيم حجازي", Type = "أعلاف", Variety = "حجازي", FarmName = "مزرعة الماشية", Area = 35.5m, PlantingDate = DateTime.Now.AddDays(-60), ExpectedHarvestDate = DateTime.Now.AddDays(30), Status = "نمو", Notes = "نمو سريع" }
        };
        
        _filteredCrops = _crops.ToList();
        _loading = false;
    }

    private void SearchCrops()
    {
        ApplyFilters();
    }

    private void FilterByType()
    {
        ApplyFilters();
    }

    private void FilterByStatus()
    {
        ApplyFilters();
    }

    private void ApplyFilters()
    {
        _filteredCrops = _crops.Where(c =>
            (string.IsNullOrEmpty(_searchText) || c.Name.Contains(_searchText, StringComparison.OrdinalIgnoreCase) || c.Type.Contains(_searchText, StringComparison.OrdinalIgnoreCase)) &&
            (string.IsNullOrEmpty(_selectedType) || c.Type == _selectedType) &&
            (string.IsNullOrEmpty(_selectedStatus) || c.Status == _selectedStatus)
        ).ToList();
    }

    private void ResetFilters()
    {
        _searchText = string.Empty;
        _selectedType = string.Empty;
        _selectedStatus = string.Empty;
        _filteredCrops = _crops.ToList();
    }

    private void OpenAddDialog()
    {
        _currentCrop = new Crop();
        _isEditing = false;
        _showAddEditDialog = true;
    }

    private void ViewCrop(Crop crop)
    {
        // TODO: Navigate to crop details page
    }

    private void EditCrop(Crop crop)
    {
        _currentCrop = new Crop
        {
            Id = crop.Id,
            Name = crop.Name,
            Type = crop.Type,
            Variety = crop.Variety,
            FarmName = crop.FarmName,
            Area = crop.Area,
            PlantingDate = crop.PlantingDate,
            ExpectedHarvestDate = crop.ExpectedHarvestDate,
            Status = crop.Status,
            Notes = crop.Notes
        };
        _isEditing = true;
        _showAddEditDialog = true;
    }

    private void ViewProgress(Crop crop)
    {
        // TODO: Navigate to crop progress page
    }

    private async Task DeleteCrop(Crop crop)
    {
        // TODO: Show confirmation dialog and delete
    }

    private void CloseDialog()
    {
        _showAddEditDialog = false;
        _currentCrop = new();
    }

    private async Task SaveCrop()
    {
        // TODO: Validate and save crop
        _showAddEditDialog = false;
        await LoadCrops();
    }

    private string GetCropIcon(string type)
    {
        return type switch
        {
            "خضروات" => Icons.Material.Filled.Eco,
            "فواكه" => Icons.Material.Filled.LocalFlorist,
            "حبوب" => Icons.Material.Filled.Grain,
            "بقوليات" => Icons.Material.Filled.Grass,
            "أعلاف" => Icons.Material.Filled.Agriculture,
            _ => Icons.Material.Filled.Eco
        };
    }

    private Color GetTypeColor(string type)
    {
        return type switch
        {
            "خضروات" => Color.Success,
            "فواكه" => Color.Warning,
            "حبوب" => Color.Info,
            "بقوليات" => Color.Secondary,
            "أعلاف" => Color.Primary,
            _ => Color.Default
        };
    }

    private Color GetStatusColor(string status)
    {
        return status switch
        {
            "زراعة" => Color.Info,
            "نمو" => Color.Primary,
            "إزهار" => Color.Warning,
            "نضج" => Color.Success,
            "حصاد" => Color.Tertiary,
            "مكتمل" => Color.Dark,
            _ => Color.Default
        };
    }

    // النموذج
    public class Crop
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public string Variety { get; set; } = string.Empty;
        public string FarmName { get; set; } = string.Empty;
        public decimal Area { get; set; }
        public DateTime PlantingDate { get; set; } = DateTime.Now;
        public DateTime ExpectedHarvestDate { get; set; } = DateTime.Now.AddDays(90);
        public string Status { get; set; } = "زراعة";
        public string Notes { get; set; } = string.Empty;
    }
}
