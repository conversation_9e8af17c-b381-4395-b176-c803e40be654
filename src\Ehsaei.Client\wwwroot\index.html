<!DOCTYPE html>
<html lang="ar" dir="rtl">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <title>نظام إحصائي - وزارة الزراعة والثروة السمكية</title>
    <base href="/" />
    
    <!-- Favicon -->
    <link rel="icon" type="image/png" href="favicon.png" />
    
    <!-- Google Fonts - Cairo for Arabic -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <!-- Material Design Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700&display=swap" rel="stylesheet" />
    
    <!-- MudBlazor CSS -->
    <link href="_content/MudBlazor/MudBlazor.min.css" rel="stylesheet" />
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="css/app.css" rel="stylesheet" />
    
    <!-- App Manifest -->
    <link rel="manifest" href="manifest.json" />
    <link rel="apple-touch-icon" sizes="512x512" href="icon-512.png" />
    <link rel="apple-touch-icon" sizes="192x192" href="icon-192.png" />
    
    <!-- Meta Tags for SEO -->
    <meta name="description" content="نظام إحصائي متطور لوزارة الزراعة والثروة السمكية في سلطنة عمان" />
    <meta name="keywords" content="إحصائي، زراعة، ثروة سمكية، عمان، نظام، إدارة" />
    <meta name="author" content="وزارة الزراعة والثروة السمكية" />
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="نظام إحصائي - وزارة الزراعة والثروة السمكية" />
    <meta property="og:description" content="نظام إحصائي متطور لإدارة ومراقبة القطاعات الزراعية والثروة السمكية" />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://ehsaei.gov.om" />
    <meta property="og:image" content="icon-512.png" />
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="نظام إحصائي - وزارة الزراعة والثروة السمكية" />
    <meta name="twitter:description" content="نظام إحصائي متطور لإدارة ومراقبة القطاعات الزراعية والثروة السمكية" />
    <meta name="twitter:image" content="icon-512.png" />
    
    <!-- Theme Color -->
    <meta name="theme-color" content="#2196f3" />
    <meta name="msapplication-navbutton-color" content="#2196f3" />
    <meta name="apple-mobile-web-app-status-bar-style" content="#2196f3" />
    
    <!-- PWA Settings -->
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <meta name="apple-mobile-web-app-title" content="نظام إحصائي" />
    
    <style>
        /* Loading Screen */
        .loading-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            color: white;
            font-family: 'Cairo', 'Roboto', sans-serif;
        }
        
        .loading-logo {
            width: 80px;
            height: 80px;
            margin-bottom: 20px;
            animation: pulse 2s infinite;
        }
        
        .loading-text {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 10px;
        }
        
        .loading-subtitle {
            font-size: 14px;
            opacity: 0.8;
            margin-bottom: 30px;
        }
        
        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
        
        /* RTL Support */
        body {
            font-family: 'Cairo', 'Roboto', sans-serif;
            direction: rtl;
            text-align: right;
        }
        
        /* Custom Scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }
        
        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }
        
        ::-webkit-scrollbar-thumb {
            background: #2196f3;
            border-radius: 4px;
        }
        
        ::-webkit-scrollbar-thumb:hover {
            background: #1976d2;
        }
    </style>
</head>

<body>
    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-container">
        <div class="loading-logo">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4M12,6A6,6 0 0,0 6,12A6,6 0 0,0 12,18A6,6 0 0,0 18,12A6,6 0 0,0 12,6M12,8A4,4 0 0,1 16,12A4,4 0 0,1 12,16A4,4 0 0,1 8,12A4,4 0 0,1 12,8Z" />
            </svg>
        </div>
        <div class="loading-text">نظام إحصائي</div>
        <div class="loading-subtitle">وزارة الزراعة والثروة السمكية</div>
        <div class="loading-spinner"></div>
    </div>

    <!-- App Container -->
    <div id="app">
        <div class="loading-container">
            <div class="loading-logo">
                <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4M12,6A6,6 0 0,0 6,12A6,6 0 0,0 12,18A6,6 0 0,0 18,12A6,6 0 0,0 12,6M12,8A4,4 0 0,1 16,12A4,4 0 0,1 12,16A4,4 0 0,1 8,12A4,4 0 0,1 12,8Z" />
                </svg>
            </div>
            <div class="loading-text">جاري التحميل...</div>
            <div class="loading-subtitle">يرجى الانتظار</div>
            <div class="loading-spinner"></div>
        </div>
    </div>

    <!-- Blazor Error UI -->
    <div id="blazor-error-ui">
        حدث خطأ غير متوقع. يرجى 
        <a href="" class="reload">إعادة تحميل الصفحة</a>.
        <a class="dismiss">🗙</a>
    </div>

    <!-- Scripts -->
    <script src="_framework/blazor.webassembly.js"></script>
    <script src="_content/MudBlazor/MudBlazor.min.js"></script>
    
    <!-- Culture Script -->
    <script>
        window.blazorCulture = {
            get: () => window.localStorage['BlazorCulture'],
            set: (value) => window.localStorage['BlazorCulture'] = value
        };
    </script>
    
    <!-- Service Worker -->
    <script>
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.register('service-worker.js');
        }
    </script>
    
    <!-- Hide Loading Screen -->
    <script>
        window.addEventListener('load', function() {
            setTimeout(function() {
                const loadingScreen = document.getElementById('loading-screen');
                if (loadingScreen) {
                    loadingScreen.style.opacity = '0';
                    loadingScreen.style.transition = 'opacity 0.5s ease-out';
                    setTimeout(function() {
                        loadingScreen.style.display = 'none';
                    }, 500);
                }
            }, 1000);
        });
    </script>
</body>

</html>
