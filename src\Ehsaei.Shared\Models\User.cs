using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace Ehsaei.Shared.Models;

/// <summary>
/// نموذج المستخدم
/// </summary>
public class User
{
    public int Id { get; set; }

    [Required(ErrorMessage = "اسم المستخدم مطلوب")]
    [StringLength(50, ErrorMessage = "اسم المستخدم يجب أن يكون أقل من 50 حرف")]
    public string Username { get; set; } = string.Empty;

    [Required(ErrorMessage = "البريد الإلكتروني مطلوب")]
    [EmailAddress(ErrorMessage = "البريد الإلكتروني غير صحيح")]
    public string Email { get; set; } = string.Empty;

    [Required(ErrorMessage = "الاسم الكامل مطلوب")]
    [StringLength(100, ErrorMessage = "الاسم الكامل يجب أن يكون أقل من 100 حرف")]
    public string FullName { get; set; } = string.Empty;

    [JsonIgnore]
    public string PasswordHash { get; set; } = string.Empty;

    public string? PhoneNumber { get; set; }

    public string? Department { get; set; }

    public string? Position { get; set; }

    public bool IsActive { get; set; } = true;

    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    public DateTime? LastLoginAt { get; set; }

    // Navigation Properties
    public virtual ICollection<UserRole> UserRoles { get; set; } = new List<UserRole>();
    public virtual ICollection<UserSector> UserSectors { get; set; } = new List<UserSector>();
}

/// <summary>
/// نموذج الدور
/// </summary>
public class Role
{
    public int Id { get; set; }

    [Required(ErrorMessage = "اسم الدور مطلوب")]
    [StringLength(50, ErrorMessage = "اسم الدور يجب أن يكون أقل من 50 حرف")]
    public string Name { get; set; } = string.Empty;

    [StringLength(200, ErrorMessage = "الوصف يجب أن يكون أقل من 200 حرف")]
    public string? Description { get; set; }

    public bool IsActive { get; set; } = true;

    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    // Navigation Properties
    public virtual ICollection<UserRole> UserRoles { get; set; } = new List<UserRole>();
    public virtual ICollection<RolePermission> RolePermissions { get; set; } = new List<RolePermission>();
}

/// <summary>
/// نموذج الصلاحية
/// </summary>
public class Permission
{
    public int Id { get; set; }

    [Required(ErrorMessage = "اسم الصلاحية مطلوب")]
    [StringLength(50, ErrorMessage = "اسم الصلاحية يجب أن يكون أقل من 50 حرف")]
    public string Name { get; set; } = string.Empty;

    [StringLength(200, ErrorMessage = "الوصف يجب أن يكون أقل من 200 حرف")]
    public string? Description { get; set; }

    [StringLength(50, ErrorMessage = "الفئة يجب أن تكون أقل من 50 حرف")]
    public string Category { get; set; } = string.Empty;

    public bool IsActive { get; set; } = true;

    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    // Navigation Properties
    public virtual ICollection<RolePermission> RolePermissions { get; set; } = new List<RolePermission>();
}

/// <summary>
/// نموذج ربط المستخدم بالدور
/// </summary>
public class UserRole
{
    public int UserId { get; set; }
    public int RoleId { get; set; }
    public DateTime AssignedAt { get; set; } = DateTime.UtcNow;
    public int? AssignedBy { get; set; }

    // Navigation Properties
    public virtual User User { get; set; } = null!;
    public virtual Role Role { get; set; } = null!;
}

/// <summary>
/// نموذج ربط الدور بالصلاحية
/// </summary>
public class RolePermission
{
    public int RoleId { get; set; }
    public int PermissionId { get; set; }
    public DateTime AssignedAt { get; set; } = DateTime.UtcNow;
    public int? AssignedBy { get; set; }

    // Navigation Properties
    public virtual Role Role { get; set; } = null!;
    public virtual Permission Permission { get; set; } = null!;
}

/// <summary>
/// نموذج القطاع
/// </summary>
public class Sector
{
    public int Id { get; set; }

    [Required(ErrorMessage = "اسم القطاع مطلوب")]
    [StringLength(100, ErrorMessage = "اسم القطاع يجب أن يكون أقل من 100 حرف")]
    public string Name { get; set; } = string.Empty;

    [StringLength(500, ErrorMessage = "الوصف يجب أن يكون أقل من 500 حرف")]
    public string? Description { get; set; }

    [StringLength(50, ErrorMessage = "الرمز يجب أن يكون أقل من 50 حرف")]
    public string? Code { get; set; }

    [StringLength(50, ErrorMessage = "الأيقونة يجب أن تكون أقل من 50 حرف")]
    public string? Icon { get; set; }

    [StringLength(20, ErrorMessage = "اللون يجب أن يكون أقل من 20 حرف")]
    public string? Color { get; set; }

    public int DisplayOrder { get; set; } = 0;

    public bool IsActive { get; set; } = true;

    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    // Navigation Properties
    public virtual ICollection<UserSector> UserSectors { get; set; } = new List<UserSector>();
    public virtual ICollection<SectorAchievement> SectorAchievements { get; set; } = new List<SectorAchievement>();
}

/// <summary>
/// نموذج ربط المستخدم بالقطاع
/// </summary>
public class UserSector
{
    public int UserId { get; set; }
    public int SectorId { get; set; }
    public DateTime AssignedAt { get; set; } = DateTime.UtcNow;
    public int? AssignedBy { get; set; }

    // Navigation Properties
    public virtual User User { get; set; } = null!;
    public virtual Sector Sector { get; set; } = null!;
}

/// <summary>
/// نموذج إنجازات القطاع
/// </summary>
public class SectorAchievement
{
    public int Id { get; set; }

    public int SectorId { get; set; }

    [Required(ErrorMessage = "العنوان مطلوب")]
    [StringLength(200, ErrorMessage = "العنوان يجب أن يكون أقل من 200 حرف")]
    public string Title { get; set; } = string.Empty;

    [StringLength(1000, ErrorMessage = "الوصف يجب أن يكون أقل من 1000 حرف")]
    public string? Description { get; set; }

    [StringLength(50, ErrorMessage = "النوع يجب أن يكون أقل من 50 حرف")]
    public string Type { get; set; } = string.Empty;

    public decimal? Value { get; set; }

    [StringLength(20, ErrorMessage = "الوحدة يجب أن تكون أقل من 20 حرف")]
    public string? Unit { get; set; }

    public DateTime AchievementDate { get; set; } = DateTime.UtcNow;

    public int CreatedBy { get; set; }

    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    public DateTime? UpdatedAt { get; set; }

    public bool IsActive { get; set; } = true;

    // Navigation Properties
    public virtual Sector Sector { get; set; } = null!;
    public virtual User CreatedByUser { get; set; } = null!;
}
