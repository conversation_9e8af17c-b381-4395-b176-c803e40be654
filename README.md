# نظام إحصائي - Ehsaei Statistics System

نظام إحصائي شامل ومتطور مطور باستخدام **ASP.NET Core API** و **Blazor WebAssembly** مع **SQL Server** لإدارة البيانات الإحصائية للقطاعات الحكومية المختلفة.

## 🚀 المميزات الرئيسية

### 🎨 واجهة مستخدم عصرية
- **تصميم عربي كامل** مع دعم RTL
- **MudBlazor** للمكونات الأنيقة والتفاعلية
- **واجهة متجاوبة** تعمل على جميع الأجهزة
- **ألوان وتصميم احترافي** مع شفافية زجاجية

### 🏗️ معمارية متقدمة
- **ASP.NET Core 8 Web API** للخدمات الخلفية
- **Blazor WebAssembly** للواجهة الأمامية
- **Entity Framework Core** لإدارة قاعدة البيانات
- **JWT Authentication** للأمان
- **Clean Architecture** مع فصل الطبقات

### 🔒 نظام أمان متكامل
- **مصادقة JWT** آمنة
- **نظام أدوار وصلاحيات** مرن
- **تشفير كلمات المرور** باستخدام BCrypt
- **حماية من الهجمات** الشائعة

### 📊 إدارة القطاعات
- **القطاع النباتي**
- **القطاع الحيواني**
- **قطاع موارد المياه**
- **قطاع جودة وسلامة الغذاء**
- **قطاع الرقابة**
- **قطاع الشؤون الإدارية والمالية**

## 🛠️ التقنيات المستخدمة

### Backend
- **.NET 8** - إطار العمل الأساسي
- **ASP.NET Core Web API** - واجهة برمجة التطبيقات
- **Entity Framework Core** - ORM لقاعدة البيانات
- **SQL Server** - قاعدة البيانات
- **JWT Bearer** - المصادقة والتفويض
- **AutoMapper** - تحويل الكائنات
- **FluentValidation** - التحقق من صحة البيانات
- **Serilog** - التسجيل والمراقبة

### Frontend
- **Blazor WebAssembly** - إطار العمل للواجهة
- **MudBlazor** - مكتبة المكونات
- **Blazored.LocalStorage** - التخزين المحلي
- **System.Net.Http.Json** - التواصل مع API

### Database
- **SQL Server** - قاعدة البيانات الرئيسية
- **Entity Framework Core** - Code First Approach

## 📁 هيكل المشروع

```
Ehsaei/
├── src/
│   ├── Ehsaei.API/              # ASP.NET Core Web API
│   │   ├── Controllers/         # تحكمات API
│   │   ├── Data/               # سياق قاعدة البيانات
│   │   ├── Models/             # نماذج قاعدة البيانات
│   │   ├── Services/           # خدمات الأعمال
│   │   ├── Middleware/         # البرمجيات الوسطية
│   │   ├── Validators/         # مدققات البيانات
│   │   └── Mappings/           # تحويل الكائنات
│   │
│   ├── Ehsaei.Client/          # Blazor WebAssembly
│   │   ├── Components/         # المكونات المخصصة
│   │   ├── Pages/              # صفحات التطبيق
│   │   ├── Services/           # خدمات العميل
│   │   ├── Shared/             # المكونات المشتركة
│   │   └── wwwroot/            # الملفات الثابتة
│   │
│   └── Ehsaei.Shared/          # المشروع المشترك
│       ├── Models/             # النماذج المشتركة
│       └── DTOs/               # كائنات نقل البيانات
│
├── docs/                       # التوثيق
└── README.md                   # هذا الملف
```

## ⚙️ متطلبات النظام

### الحد الأدنى
- **نظام التشغيل**: Windows 10/11, macOS, Linux
- **إطار العمل**: .NET 8 SDK
- **قاعدة البيانات**: SQL Server 2019 أو أحدث
- **المتصفح**: Chrome, Firefox, Safari, Edge (أحدث إصدار)
- **الذاكرة**: 4 جيجابايت RAM
- **التخزين**: 2 جيجابايت مساحة فارغة

### المستحسن
- **نظام التشغيل**: Windows 11, macOS Monterey+, Ubuntu 22.04+
- **إطار العمل**: .NET 8 SDK (أحدث إصدار)
- **قاعدة البيانات**: SQL Server 2022
- **IDE**: Visual Studio 2022, VS Code, Rider
- **الذاكرة**: 8 جيجابايت RAM أو أكثر
- **التخزين**: 5 جيجابايت مساحة فارغة

## 🚀 التثبيت والتشغيل

### 1. استنساخ المشروع
```bash
git clone https://github.com/your-repo/ehsaei-system.git
cd ehsaei-system
```

### 2. إعداد قاعدة البيانات
```bash
# تحديث connection string في appsettings.json
# تشغيل migrations
cd src/Ehsaei.API
dotnet ef database update
```

### 3. تشغيل API
```bash
cd src/Ehsaei.API
dotnet run
```

### 4. تشغيل العميل
```bash
cd src/Ehsaei.Client
dotnet run
```

### 5. الوصول للتطبيق
- **API**: https://localhost:7000
- **العميل**: https://localhost:7001
- **Swagger**: https://localhost:7000/swagger

## 🔧 الإعدادات

### إعدادات API (appsettings.json)
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=localhost;Database=EhsaeiDB;Integrated Security=true;"
  },
  "JwtSettings": {
    "SecretKey": "your-secret-key",
    "Issuer": "EhsaeiAPI",
    "Audience": "EhsaeiClient",
    "ExpiryInHours": 24
  }
}
```

## 👥 المستخدمون الافتراضيون

| المستخدم | كلمة المرور | الدور | الوصف |
|----------|-------------|-------|--------|
| admin | Admin@123 | مدير النظام | صلاحيات كاملة |
| manager | Manager@123 | مدير قطاع | إدارة قطاع محدد |
| analyst | Analyst@123 | محلل إحصائي | عرض وتحليل البيانات |
| operator | Operator@123 | مدخل بيانات | إدخال البيانات |

## 📊 القطاعات المدعومة

### 🌱 القطاع النباتي
- إحصائيات المحاصيل
- بيانات الإنتاج الزراعي
- مراقبة الآفات والأمراض

### 🐄 القطاع الحيواني
- إحصائيات الثروة الحيوانية
- بيانات الإنتاج الحيواني
- الصحة الحيوانية

### 💧 قطاع موارد المياه
- إحصائيات استهلاك المياه
- مصادر المياه
- جودة المياه

### 🍎 قطاع جودة وسلامة الغذاء
- فحوصات الجودة
- شهادات السلامة
- المخالفات والإجراءات

### 👮 قطاع الرقابة
- التفتيش والمراقبة
- المخالفات
- الإجراءات التصحيحية

### 💼 قطاع الشؤون الإدارية والمالية
- الموارد البشرية
- الميزانيات
- المشتريات والعقود

## 🔄 التطوير

### إضافة قطاع جديد
1. إنشاء نموذج في `Ehsaei.Shared/Models`
2. إضافة Controller في `Ehsaei.API/Controllers`
3. إنشاء صفحة في `Ehsaei.Client/Pages`
4. تحديث قاعدة البيانات

### إضافة صلاحية جديدة
1. تحديث `Permission` model
2. إضافة البيانات الأولية
3. تطبيق في Controllers
4. تحديث الواجهة

## 🧪 الاختبار

```bash
# تشغيل اختبارات API
cd src/Ehsaei.API.Tests
dotnet test

# تشغيل اختبارات العميل
cd src/Ehsaei.Client.Tests
dotnet test
```

## 📚 التوثيق

- **API Documentation**: متوفر عبر Swagger UI
- **User Guide**: في مجلد `docs/`
- **Developer Guide**: في مجلد `docs/developer/`

## 🤝 المساهمة

1. Fork المشروع
2. إنشاء branch للميزة الجديدة
3. Commit التغييرات
4. Push إلى Branch
5. إنشاء Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 📞 الدعم والتواصل

- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: +966 11 123 4567
- **الموقع**: https://ehsaei.gov.sa

---

**© 2024 نظام إحصائي - جميع الحقوق محفوظة**
