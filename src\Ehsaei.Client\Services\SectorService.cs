using Ehsaei.Shared.DTOs;

namespace Ehsaei.Client.Services;

public interface ISectorService
{
    Task<List<SectorDto>> GetSectorsAsync();
    Task<SectorDto?> GetSectorByIdAsync(int id);
    Task<List<SectorAchievementDto>> GetSectorAchievementsAsync(int sectorId);
}

public class SectorService : ISectorService
{
    private readonly IApiService _apiService;

    public SectorService(IApiService apiService)
    {
        _apiService = apiService;
    }

    public async Task<List<SectorDto>> GetSectorsAsync()
    {
        // محاكاة البيانات للاختبار
        await Task.Delay(300);
        
        return new List<SectorDto>
        {
            new()
            {
                Id = 1,
                Name = "القطاع النباتي",
                Description = "إدارة ومراقبة الإنتاج الزراعي والمحاصيل النباتية",
                Code = "PLANT",
                Icon = "eco",
                Color = "#4caf50",
                DisplayOrder = 1,
                IsActive = true,
                AchievementsCount = 156,
                UsersCount = 23
            },
            new()
            {
                Id = 2,
                Name = "القطاع الحيواني",
                Description = "إدارة الثروة الحيوانية والإنتاج الحيواني",
                Code = "ANIMAL",
                Icon = "pets",
                Color = "#ff9800",
                DisplayOrder = 2,
                IsActive = true,
                AchievementsCount = 89,
                UsersCount = 18
            },
            new()
            {
                Id = 3,
                Name = "قطاع موارد المياه",
                Description = "إدارة ومراقبة موارد المياه واستهلاكها",
                Code = "WATER",
                Icon = "water",
                Color = "#2196f3",
                DisplayOrder = 3,
                IsActive = true,
                AchievementsCount = 67,
                UsersCount = 15
            },
            new()
            {
                Id = 4,
                Name = "قطاع جودة وسلامة الغذاء",
                Description = "مراقبة جودة وسلامة المنتجات الغذائية",
                Code = "FOOD_SAFETY",
                Icon = "food_bank",
                Color = "#f44336",
                DisplayOrder = 4,
                IsActive = true,
                AchievementsCount = 234,
                UsersCount = 31
            },
            new()
            {
                Id = 5,
                Name = "قطاع الرقابة",
                Description = "التفتيش والمراقبة والإجراءات التصحيحية",
                Code = "MONITORING",
                Icon = "security",
                Color = "#424242",
                DisplayOrder = 5,
                IsActive = true,
                AchievementsCount = 178,
                UsersCount = 27
            },
            new()
            {
                Id = 6,
                Name = "قطاع الشؤون الإدارية والمالية",
                Description = "إدارة الموارد البشرية والشؤون المالية",
                Code = "ADMIN_FINANCE",
                Icon = "business",
                Color = "#9c27b0",
                DisplayOrder = 6,
                IsActive = true,
                AchievementsCount = 95,
                UsersCount = 42
            }
        };
    }

    public async Task<SectorDto?> GetSectorByIdAsync(int id)
    {
        var sectors = await GetSectorsAsync();
        return sectors.FirstOrDefault(s => s.Id == id);
    }

    public async Task<List<SectorAchievementDto>> GetSectorAchievementsAsync(int sectorId)
    {
        // محاكاة البيانات للاختبار
        await Task.Delay(400);
        
        return new List<SectorAchievementDto>
        {
            new()
            {
                Id = 1,
                SectorId = sectorId,
                SectorName = "القطاع النباتي",
                Title = "زيادة إنتاج الطماطم",
                Description = "تم تحقيق زيادة 25% في إنتاج الطماطم مقارنة بالعام الماضي",
                Type = "إنتاج",
                Value = 2500.5m,
                Unit = "طن",
                AchievementDate = DateTime.Now.AddDays(-15),
                CreatedByName = "أحمد محمد",
                CreatedAt = DateTime.Now.AddDays(-15),
                IsActive = true
            },
            new()
            {
                Id = 2,
                SectorId = sectorId,
                SectorName = "القطاع النباتي",
                Title = "تطوير أصناف جديدة",
                Description = "تم تطوير 3 أصناف جديدة من الخضروات المقاومة للجفاف",
                Type = "تطوير",
                Value = 3,
                Unit = "صنف",
                AchievementDate = DateTime.Now.AddDays(-30),
                CreatedByName = "فاطمة علي",
                CreatedAt = DateTime.Now.AddDays(-30),
                IsActive = true
            }
        };
    }
}
