using Blazored.LocalStorage;
using Microsoft.AspNetCore.Components.Authorization;
using System.Net.Http.Json;
using System.Text.Json;
using Ehsaei.Shared.DTOs;

namespace Ehsaei.Client.Services;

public interface IAuthService
{
    Task<LoginResponse> LoginAsync(LoginRequest request);
    Task LogoutAsync();
    Task<bool> IsAuthenticatedAsync();
    Task<UserDto?> GetCurrentUserAsync();
}

public class AuthService : IAuthService
{
    private readonly HttpClient _httpClient;
    private readonly ILocalStorageService _localStorage;
    private readonly AuthenticationStateProvider _authStateProvider;

    public AuthService(HttpClient httpClient, ILocalStorageService localStorage, AuthenticationStateProvider authStateProvider)
    {
        _httpClient = httpClient;
        _localStorage = localStorage;
        _authStateProvider = authStateProvider;
    }

    public async Task<LoginResponse> LoginAsync(LoginRequest request)
    {
        try
        {
            // محاكاة تسجيل الدخول - سيتم استبدالها بـ API حقيقي لاحقاً
            await Task.Delay(1000); // محاكاة زمن الاستجابة

            // بيانات وهمية للاختبار
            if (request.Username == "admin" && request.Password == "admin")
            {
                var user = new UserDto
                {
                    Id = 1,
                    Username = "admin",
                    Email = "<EMAIL>",
                    FullName = "مدير النظام",
                    Department = "تقنية المعلومات",
                    Position = "مدير النظام",
                    IsActive = true,
                    CreatedAt = DateTime.Now.AddYears(-1),
                    LastLoginAt = DateTime.Now,
                    Roles = new List<string> { "مدير النظام", "مدير قطاع" },
                    Permissions = new List<string> { "عرض", "إضافة", "تعديل", "حذف", "إدارة المستخدمين" },
                    Sectors = new List<SectorDto>
                    {
                        new() { Id = 1, Name = "القطاع النباتي", Code = "PLANT", Icon = "eco", Color = "#4caf50" },
                        new() { Id = 2, Name = "القطاع الحيواني", Code = "ANIMAL", Icon = "pets", Color = "#ff9800" }
                    }
                };

                var token = "fake-jwt-token-for-testing";
                var response = new LoginResponse
                {
                    IsSuccess = true,
                    Token = token,
                    ExpiresAt = DateTime.Now.AddHours(24),
                    User = user,
                    Message = "تم تسجيل الدخول بنجاح"
                };

                // حفظ التوكن والمستخدم في التخزين المحلي
                await _localStorage.SetItemAsync("authToken", token);
                await _localStorage.SetItemAsync("currentUser", user);

                // تحديث حالة المصادقة
                ((CustomAuthStateProvider)_authStateProvider).NotifyUserAuthentication(token);

                return response;
            }
            else
            {
                return new LoginResponse
                {
                    IsSuccess = false,
                    Message = "اسم المستخدم أو كلمة المرور غير صحيحة",
                    Errors = new List<string> { "بيانات الدخول غير صحيحة" }
                };
            }
        }
        catch (Exception ex)
        {
            return new LoginResponse
            {
                IsSuccess = false,
                Message = "حدث خطأ أثناء تسجيل الدخول",
                Errors = new List<string> { ex.Message }
            };
        }
    }

    public async Task LogoutAsync()
    {
        await _localStorage.RemoveItemAsync("authToken");
        await _localStorage.RemoveItemAsync("currentUser");
        ((CustomAuthStateProvider)_authStateProvider).NotifyUserLogout();
    }

    public async Task<bool> IsAuthenticatedAsync()
    {
        var token = await _localStorage.GetItemAsync<string>("authToken");
        return !string.IsNullOrEmpty(token);
    }

    public async Task<UserDto?> GetCurrentUserAsync()
    {
        try
        {
            var user = await _localStorage.GetItemAsync<UserDto>("currentUser");
            return user;
        }
        catch
        {
            return null;
        }
    }
}
