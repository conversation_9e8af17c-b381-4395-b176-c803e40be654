<Project Sdk="Microsoft.NET.Sdk.BlazorWebAssembly">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <AssemblyTitle>نظام إحصائي - العميل</AssemblyTitle>
    <AssemblyDescription>واجهة المستخدم لنظام إحصائي</AssemblyDescription>
    <AssemblyCompany>نظام إحصائي</AssemblyCompany>
    <AssemblyProduct>Ehsaei Statistics System Client</AssemblyProduct>
    <AssemblyCopyright>© 2024 نظام إحصائي</AssemblyCopyright>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
    <ServiceWorkerAssetsManifest>service-worker-assets.js</ServiceWorkerAssetsManifest>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly" Version="8.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly.DevServer" Version="8.0.0" PrivateAssets="all" />
    <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly.Authentication" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="8.0.0" />
    <PackageReference Include="System.Net.Http.Json" Version="8.0.0" />
    <PackageReference Include="MudBlazor" Version="6.11.2" />
    <PackageReference Include="Blazored.LocalStorage" Version="4.4.0" />
    <PackageReference Include="Microsoft.AspNetCore.Components.Authorization" Version="8.0.0" />
    <PackageReference Include="System.Text.Json" Version="8.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Ehsaei.Shared\Ehsaei.Shared.csproj" />
  </ItemGroup>

  <ItemGroup>
    <ServiceWorker Include="wwwroot\service-worker.js" PublishedContent="wwwroot\service-worker.published.js" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Components\" />
    <Folder Include="Pages\" />
    <Folder Include="Services\" />
    <Folder Include="Shared\" />
    <Folder Include="wwwroot\css\" />
    <Folder Include="wwwroot\js\" />
    <Folder Include="wwwroot\images\" />
    <Folder Include="wwwroot\fonts\" />
  </ItemGroup>

</Project>
