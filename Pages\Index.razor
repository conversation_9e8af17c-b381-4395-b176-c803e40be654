@page "/"
@using MudBlazor

<PageTitle>لوحة التحكم - نظام إحصائي</PageTitle>

<MudContainer MaxWidth="MaxWidth.ExtraLarge" Class="pa-4">
    <!-- ترحيب -->
    <MudPaper Class="pa-6 mb-4" Elevation="3" Style="background: linear-gradient(135deg, rgba(33, 150, 243, 0.1) 0%, rgba(21, 101, 192, 0.1) 100%);">
        <div class="d-flex align-center justify-space-between">
            <div>
                <MudText Typo="Typo.h4" Color="Color.Primary" Class="fw-bold">مرحباً بك في نظام إحصائي</MudText>
                <MudText Typo="Typo.subtitle1" Color="Color.Secondary">وزارة الزراعة والثروة السمكية - سلطنة عمان</MudText>
                <MudText Typo="Typo.body2" Color="Color.Secondary" Class="mt-2">آخر تحديث: @DateTime.Now.ToString("yyyy/MM/dd - HH:mm")</MudText>
            </div>
            <MudIcon Icon="@Icons.Material.Filled.Dashboard" Size="Size.Large" Color="Color.Primary" />
        </div>
    </MudPaper>

    <!-- الإحصائيات العامة -->
    <MudGrid Class="mb-4">
        <MudItem xs="12" sm="6" md="3">
            <MudCard Elevation="3" Class="pa-4" Style="background: linear-gradient(135deg, #4caf50 0%, #66bb6a 100%); color: white;">
                <MudCardContent Class="pa-0">
                    <div class="d-flex align-center justify-space-between">
                        <div>
                            <MudText Typo="Typo.h4" Class="fw-bold">6</MudText>
                            <MudText Typo="Typo.body2">القطاعات النشطة</MudText>
                        </div>
                        <MudIcon Icon="@Icons.Material.Filled.Category" Size="Size.Large" />
                    </div>
                </MudCardContent>
            </MudCard>
        </MudItem>

        <MudItem xs="12" sm="6" md="3">
            <MudCard Elevation="3" Class="pa-4" Style="background: linear-gradient(135deg, #2196f3 0%, #42a5f5 100%); color: white;">
                <MudCardContent Class="pa-0">
                    <div class="d-flex align-center justify-space-between">
                        <div>
                            <MudText Typo="Typo.h4" Class="fw-bold">1,247</MudText>
                            <MudText Typo="Typo.body2">إجمالي المحاصيل</MudText>
                        </div>
                        <MudIcon Icon="@Icons.Material.Filled.Grass" Size="Size.Large" />
                    </div>
                </MudCardContent>
            </MudCard>
        </MudItem>

        <MudItem xs="12" sm="6" md="3">
            <MudCard Elevation="3" Class="pa-4" Style="background: linear-gradient(135deg, #ff9800 0%, #ffb74d 100%); color: white;">
                <MudCardContent Class="pa-0">
                    <div class="d-flex align-center justify-space-between">
                        <div>
                            <MudText Typo="Typo.h4" Class="fw-bold">15,680</MudText>
                            <MudText Typo="Typo.body2">الإنتاج الإجمالي (طن)</MudText>
                        </div>
                        <MudIcon Icon="@Icons.Material.Filled.Agriculture" Size="Size.Large" />
                    </div>
                </MudCardContent>
            </MudCard>
        </MudItem>

        <MudItem xs="12" sm="6" md="3">
            <MudCard Elevation="3" Class="pa-4" Style="background: linear-gradient(135deg, #9c27b0 0%, #ba68c8 100%); color: white;">
                <MudCardContent Class="pa-0">
                    <div class="d-flex align-center justify-space-between">
                        <div>
                            <MudText Typo="Typo.h4" Class="fw-bold">156</MudText>
                            <MudText Typo="Typo.body2">المزارع النشطة</MudText>
                        </div>
                        <MudIcon Icon="@Icons.Material.Filled.Home" Size="Size.Large" />
                    </div>
                </MudCardContent>
            </MudCard>
        </MudItem>
    </MudGrid>

    <!-- القطاعات السريعة -->
    <MudGrid>
        <MudItem xs="12" md="8">
            <MudPaper Class="pa-4" Elevation="3">
                <MudText Typo="Typo.h6" Class="mb-4" Color="Color.Primary">
                    <MudIcon Icon="@Icons.Material.Filled.TrendingUp" Class="me-2" />
                    الإنتاج الشهري لجميع القطاعات
                </MudText>
                <div class="text-center pa-8">
                    <MudIcon Icon="@Icons.Material.Filled.BarChart" Size="Size.Large" Color="Color.Primary" Class="mb-3" />
                    <MudText Typo="Typo.h6" Color="Color.Primary">الرسم البياني التفاعلي</MudText>
                    <MudText Typo="Typo.body2" Color="Color.Secondary">سيتم عرض الرسم البياني للإنتاج الشهري هنا</MudText>
                    
                    <!-- بيانات نصية بديلة -->
                    <MudGrid Class="mt-4">
                        <MudItem xs="4">
                            <MudPaper Class="pa-3" Elevation="2" Style="background: rgba(76, 175, 80, 0.1);">
                                <MudText Typo="Typo.caption" Color="Color.Success">القطاع النباتي</MudText>
                                <MudText Typo="Typo.h6" Color="Color.Success">1,680 طن</MudText>
                            </MudPaper>
                        </MudItem>
                        <MudItem xs="4">
                            <MudPaper Class="pa-3" Elevation="2" Style="background: rgba(255, 152, 0, 0.1);">
                                <MudText Typo="Typo.caption" Color="Color.Warning">القطاع الحيواني</MudText>
                                <MudText Typo="Typo.h6" Color="Color.Warning">980 طن</MudText>
                            </MudPaper>
                        </MudItem>
                        <MudItem xs="4">
                            <MudPaper Class="pa-3" Elevation="2" Style="background: rgba(33, 150, 243, 0.1);">
                                <MudText Typo="Typo.caption" Color="Color.Info">قطاع المياه</MudText>
                                <MudText Typo="Typo.h6" Color="Color.Info">490 طن</MudText>
                            </MudPaper>
                        </MudItem>
                    </MudGrid>
                </div>
            </MudPaper>
        </MudItem>

        <MudItem xs="12" md="4">
            <MudPaper Class="pa-4" Elevation="3">
                <MudText Typo="Typo.h6" Class="mb-4" Color="Color.Primary">
                    <MudIcon Icon="@Icons.Material.Filled.Speed" Class="me-2" />
                    الوصول السريع للقطاعات
                </MudText>
                <MudList>
                    <MudListItem Href="/plant-sector" Class="rounded mb-2" Style="background: rgba(76, 175, 80, 0.1);">
                        <div class="d-flex align-center">
                            <MudAvatar Color="Color.Success" Size="Size.Medium" Class="me-3">
                                <MudIcon Icon="@Icons.Material.Filled.Eco" />
                            </MudAvatar>
                            <div>
                                <MudText Typo="Typo.body1" Class="fw-bold">القطاع النباتي</MudText>
                                <MudText Typo="Typo.caption" Color="Color.Secondary">1,247 محصول نشط</MudText>
                            </div>
                        </div>
                    </MudListItem>

                    <MudListItem Href="/animal-sector" Class="rounded mb-2" Style="background: rgba(255, 152, 0, 0.1);">
                        <div class="d-flex align-center">
                            <MudAvatar Color="Color.Warning" Size="Size.Medium" Class="me-3">
                                <MudIcon Icon="@Icons.Material.Filled.Pets" />
                            </MudAvatar>
                            <div>
                                <MudText Typo="Typo.body1" Class="fw-bold">القطاع الحيواني</MudText>
                                <MudText Typo="Typo.caption" Color="Color.Secondary">856 رأس ماشية</MudText>
                            </div>
                        </div>
                    </MudListItem>

                    <MudListItem Href="/water-sector" Class="rounded mb-2" Style="background: rgba(33, 150, 243, 0.1);">
                        <div class="d-flex align-center">
                            <MudAvatar Color="Color.Info" Size="Size.Medium" Class="me-3">
                                <MudIcon Icon="@Icons.Material.Filled.Water" />
                            </MudAvatar>
                            <div>
                                <MudText Typo="Typo.body1" Class="fw-bold">قطاع موارد المياه</MudText>
                                <MudText Typo="Typo.caption" Color="Color.Secondary">45 مصدر مياه</MudText>
                            </div>
                        </div>
                    </MudListItem>

                    <MudListItem Href="/food-safety" Class="rounded mb-2" Style="background: rgba(244, 67, 54, 0.1);">
                        <div class="d-flex align-center">
                            <MudAvatar Color="Color.Error" Size="Size.Medium" Class="me-3">
                                <MudIcon Icon="@Icons.Material.Filled.FoodBank" />
                            </MudAvatar>
                            <div>
                                <MudText Typo="Typo.body1" Class="fw-bold">جودة وسلامة الغذاء</MudText>
                                <MudText Typo="Typo.caption" Color="Color.Secondary">234 فحص جودة</MudText>
                            </div>
                        </div>
                    </MudListItem>
                </MudList>
            </MudPaper>
        </MudItem>
    </MudGrid>
</MudContainer>
