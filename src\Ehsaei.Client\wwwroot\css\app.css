/* ===== نظام إحصائي - الأنماط المخصصة ===== */

/* ===== الخطوط والنصوص ===== */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap');

* {
    font-family: 'Cairo', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
}

body {
    direction: rtl;
    text-align: right;
    font-family: 'Cairo', 'Roboto', sans-serif;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
}

/* ===== الألوان المخصصة ===== */
:root {
    --primary-color: #2196f3;
    --primary-dark: #1976d2;
    --primary-light: #bbdefb;
    --secondary-color: #ff9800;
    --success-color: #4caf50;
    --warning-color: #ff5722;
    --error-color: #f44336;
    --info-color: #00bcd4;
    --dark-color: #424242;
    
    --background-gradient: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    --card-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    --glass-effect: rgba(255, 255, 255, 0.25);
    --glass-border: rgba(255, 255, 255, 0.18);
}

/* ===== التأثيرات الزجاجية ===== */
.glass-effect {
    background: var(--glass-effect);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    box-shadow: var(--card-shadow);
}

.glass-card {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* ===== التدرجات اللونية ===== */
.gradient-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
}

.gradient-success {
    background: linear-gradient(135deg, #4caf50 0%, #66bb6a 100%);
}

.gradient-warning {
    background: linear-gradient(135deg, #ff9800 0%, #ffb74d 100%);
}

.gradient-error {
    background: linear-gradient(135deg, #f44336 0%, #ef5350 100%);
}

.gradient-info {
    background: linear-gradient(135deg, #00bcd4 0%, #26c6da 100%);
}

.gradient-secondary {
    background: linear-gradient(135deg, #9c27b0 0%, #ba68c8 100%);
}

/* ===== الحركات والانتقالات ===== */
.smooth-transition {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.hover-scale:hover {
    transform: scale(1.02);
}

.fade-in {
    animation: fadeIn 0.6s ease-in-out;
}

.slide-in-right {
    animation: slideInRight 0.6s ease-out;
}

.slide-in-left {
    animation: slideInLeft 0.6s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideInRight {
    from { 
        opacity: 0;
        transform: translateX(30px);
    }
    to { 
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInLeft {
    from { 
        opacity: 0;
        transform: translateX(-30px);
    }
    to { 
        opacity: 1;
        transform: translateX(0);
    }
}

/* ===== البطاقات المخصصة ===== */
.stat-card {
    border-radius: 16px;
    overflow: hidden;
    position: relative;
    transition: all 0.3s ease;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    pointer-events: none;
}

.stat-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
}

/* ===== القوائم الجانبية ===== */
.sidebar-gradient {
    background: linear-gradient(180deg, 
        rgba(33, 150, 243, 0.95) 0%, 
        rgba(21, 101, 192, 0.95) 50%,
        rgba(13, 71, 161, 0.95) 100%);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.nav-item-hover {
    transition: all 0.3s ease;
    border-radius: 12px;
    margin: 4px 8px;
}

.nav-item-hover:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateX(-4px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.nav-item-active {
    background: rgba(255, 255, 255, 0.2);
    border-right: 4px solid #ffffff;
    font-weight: 600;
}

/* ===== الجداول المخصصة ===== */
.custom-table {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: var(--card-shadow);
}

.custom-table .mud-table-head {
    background: linear-gradient(90deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: white;
}

.custom-table .mud-table-row:hover {
    background: rgba(33, 150, 243, 0.05);
    transform: scale(1.01);
    transition: all 0.2s ease;
}

/* ===== الأزرار المخصصة ===== */
.btn-gradient {
    background: linear-gradient(45deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    border: none;
    border-radius: 25px;
    padding: 12px 24px;
    color: white;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(33, 150, 243, 0.3);
}

.btn-gradient:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(33, 150, 243, 0.4);
}

.btn-glass {
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 12px;
    color: var(--primary-color);
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-glass:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
}

/* ===== الحقول والنماذج ===== */
.form-glass {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    padding: 24px;
    box-shadow: var(--card-shadow);
}

.input-rtl {
    direction: rtl;
    text-align: right;
}

/* ===== الرسوم البيانية ===== */
.chart-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 20px;
    box-shadow: var(--card-shadow);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* ===== التنبيهات والإشعارات ===== */
.alert-glass {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 12px;
    box-shadow: var(--card-shadow);
}

/* ===== شريط التمرير المخصص ===== */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(45deg, var(--primary-color), var(--primary-dark));
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(45deg, var(--primary-dark), var(--primary-color));
}

/* ===== الاستجابة للشاشات الصغيرة ===== */
@media (max-width: 768px) {
    .mobile-hide {
        display: none !important;
    }
    
    .mobile-full {
        width: 100% !important;
    }
    
    .mobile-center {
        text-align: center !important;
    }
    
    .stat-card {
        margin-bottom: 16px;
    }
    
    .chart-container {
        padding: 12px;
    }
}

/* ===== تحسينات الطباعة ===== */
@media print {
    .no-print {
        display: none !important;
    }
    
    body {
        background: white !important;
    }
    
    .glass-effect,
    .glass-card,
    .chart-container {
        background: white !important;
        box-shadow: none !important;
        border: 1px solid #ddd !important;
    }
}

/* ===== إصلاحات MudBlazor للعربية ===== */
.mud-input-root {
    direction: rtl;
}

.mud-input-root input {
    text-align: right;
}

.mud-select {
    direction: rtl;
}

.mud-table {
    direction: rtl;
}

.mud-table th,
.mud-table td {
    text-align: right;
}

.mud-nav-link {
    direction: rtl;
    text-align: right;
}

.mud-breadcrumbs {
    direction: rtl;
}

/* ===== تحسينات إضافية ===== */
.page-header {
    background: linear-gradient(135deg, rgba(33, 150, 243, 0.1) 0%, rgba(21, 101, 192, 0.05) 100%);
    border-radius: 16px;
    padding: 24px;
    margin-bottom: 24px;
    border: 1px solid rgba(33, 150, 243, 0.1);
}

.section-divider {
    height: 2px;
    background: linear-gradient(90deg, transparent 0%, var(--primary-color) 50%, transparent 100%);
    margin: 32px 0;
    border: none;
}

.floating-action {
    position: fixed;
    bottom: 24px;
    left: 24px;
    z-index: 1000;
    border-radius: 50%;
    width: 56px;
    height: 56px;
    box-shadow: 0 8px 24px rgba(33, 150, 243, 0.3);
}

.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-left: 8px;
}

.status-active {
    background: #4caf50;
    box-shadow: 0 0 8px rgba(76, 175, 80, 0.5);
}

.status-inactive {
    background: #f44336;
    box-shadow: 0 0 8px rgba(244, 67, 54, 0.5);
}

.status-pending {
    background: #ff9800;
    box-shadow: 0 0 8px rgba(255, 152, 0, 0.5);
}
