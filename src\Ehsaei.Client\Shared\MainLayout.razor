@using MudBlazor
@inherits LayoutView

<MudThemeProvider @ref="@_mudThemeProvider" @bind-IsDarkMode="@_isDarkMode" Theme="@_theme" />
<MudDialogProvider />
<MudSnackbarProvider />

<MudLayout>
    <!-- القائمة الجانبية -->
    <MudDrawer @bind-Open="_drawerOpen" Elevation="2" Variant="@DrawerVariant.Responsive" ClipMode="DrawerClipMode.Always">
        <MudDrawerHeader Class="pa-6" Style="background: linear-gradient(135deg, rgba(33, 150, 243, 0.9) 0%, rgba(21, 101, 192, 0.9) 100%); backdrop-filter: blur(10px);">
            <div class="d-flex align-center">
                <MudIcon Icon="@Icons.Material.Filled.Analytics" Size="Size.Large" Color="Color.Surface" Class="me-3" />
                <div>
                    <MudText Typo="Typo.h6" Color="Color.Surface" Class="fw-bold">نظام إحصائي</MudText>
                    <MudText Typo="Typo.caption" Color="Color.Surface" Style="opacity: 0.8;">وزارة الزراعة والثروة السمكية</MudText>
                </div>
            </div>
        </MudDrawerHeader>
        
        <MudNavMenu Class="pa-2" Style="background: linear-gradient(180deg, rgba(33, 150, 243, 0.1) 0%, rgba(21, 101, 192, 0.05) 100%);">
            <!-- لوحة التحكم -->
            <MudNavLink Href="/" Match="NavLinkMatch.All" Icon="@Icons.Material.Filled.Dashboard" IconColor="Color.Primary">
                <MudText Class="nav-text">لوحة التحكم</MudText>
            </MudNavLink>
            
            <MudDivider Class="my-2" />
            
            <!-- القطاعات -->
            <MudText Typo="Typo.caption" Class="px-4 py-2 text-muted">القطاعات</MudText>
            
            <!-- القطاع النباتي -->
            <MudNavGroup Text="القطاع النباتي" Icon="@Icons.Material.Filled.Eco" IconColor="Color.Success" Expanded="true">
                <MudNavLink Href="/plant-sector" Icon="@Icons.Material.Filled.Dashboard" IconColor="Color.Success">
                    <MudText Class="nav-text">نظرة عامة</MudText>
                </MudNavLink>
                <MudNavLink Href="/plant-sector/crops" Icon="@Icons.Material.Filled.Grass" IconColor="Color.Success">
                    <MudText Class="nav-text">المحاصيل</MudText>
                </MudNavLink>
                <MudNavLink Href="/plant-sector/production" Icon="@Icons.Material.Filled.Agriculture" IconColor="Color.Success">
                    <MudText Class="nav-text">الإنتاج الزراعي</MudText>
                </MudNavLink>
                <MudNavLink Href="/plant-sector/diseases" Icon="@Icons.Material.Filled.BugReport" IconColor="Color.Warning">
                    <MudText Class="nav-text">الآفات والأمراض</MudText>
                </MudNavLink>
                <MudNavLink Href="/plant-sector/irrigation" Icon="@Icons.Material.Filled.Water" IconColor="Color.Info">
                    <MudText Class="nav-text">الري والمياه</MudText>
                </MudNavLink>
                <MudNavLink Href="/plant-sector/fertilizers" Icon="@Icons.Material.Filled.Science" IconColor="Color.Secondary">
                    <MudText Class="nav-text">الأسمدة والمبيدات</MudText>
                </MudNavLink>
            </MudNavGroup>
            
            <!-- القطاع الحيواني -->
            <MudNavGroup Text="القطاع الحيواني" Icon="@Icons.Material.Filled.Pets" IconColor="Color.Warning">
                <MudNavLink Href="/animal-sector" Icon="@Icons.Material.Filled.Dashboard">
                    <MudText Class="nav-text">نظرة عامة</MudText>
                </MudNavLink>
                <MudNavLink Href="/animal-sector/livestock" Icon="@Icons.Material.Filled.Pets">
                    <MudText Class="nav-text">الثروة الحيوانية</MudText>
                </MudNavLink>
                <MudNavLink Href="/animal-sector/production" Icon="@Icons.Material.Filled.Egg">
                    <MudText Class="nav-text">الإنتاج الحيواني</MudText>
                </MudNavLink>
                <MudNavLink Href="/animal-sector/health" Icon="@Icons.Material.Filled.HealthAndSafety">
                    <MudText Class="nav-text">الصحة الحيوانية</MudText>
                </MudNavLink>
            </MudNavGroup>
            
            <!-- قطاع موارد المياه -->
            <MudNavGroup Text="قطاع موارد المياه" Icon="@Icons.Material.Filled.Water" IconColor="Color.Info">
                <MudNavLink Href="/water-sector" Icon="@Icons.Material.Filled.Dashboard">
                    <MudText Class="nav-text">نظرة عامة</MudText>
                </MudNavLink>
                <MudNavLink Href="/water-sector/consumption" Icon="@Icons.Material.Filled.WaterDrop">
                    <MudText Class="nav-text">استهلاك المياه</MudText>
                </MudNavLink>
                <MudNavLink Href="/water-sector/sources" Icon="@Icons.Material.Filled.Waves">
                    <MudText Class="nav-text">مصادر المياه</MudText>
                </MudNavLink>
                <MudNavLink Href="/water-sector/quality" Icon="@Icons.Material.Filled.Science">
                    <MudText Class="nav-text">جودة المياه</MudText>
                </MudNavLink>
            </MudNavGroup>
            
            <!-- قطاع جودة وسلامة الغذاء -->
            <MudNavGroup Text="جودة وسلامة الغذاء" Icon="@Icons.Material.Filled.FoodBank" IconColor="Color.Error">
                <MudNavLink Href="/food-safety" Icon="@Icons.Material.Filled.Dashboard">
                    <MudText Class="nav-text">نظرة عامة</MudText>
                </MudNavLink>
                <MudNavLink Href="/food-safety/inspections" Icon="@Icons.Material.Filled.Search">
                    <MudText Class="nav-text">فحوصات الجودة</MudText>
                </MudNavLink>
                <MudNavLink Href="/food-safety/certificates" Icon="@Icons.Material.Filled.Certificate">
                    <MudText Class="nav-text">شهادات السلامة</MudText>
                </MudNavLink>
                <MudNavLink Href="/food-safety/violations" Icon="@Icons.Material.Filled.Warning">
                    <MudText Class="nav-text">المخالفات</MudText>
                </MudNavLink>
            </MudNavGroup>
            
            <!-- قطاع الرقابة -->
            <MudNavGroup Text="قطاع الرقابة" Icon="@Icons.Material.Filled.Security" IconColor="Color.Dark">
                <MudNavLink Href="/monitoring" Icon="@Icons.Material.Filled.Dashboard">
                    <MudText Class="nav-text">نظرة عامة</MudText>
                </MudNavLink>
                <MudNavLink Href="/monitoring/inspections" Icon="@Icons.Material.Filled.Assignment">
                    <MudText Class="nav-text">التفتيش والمراقبة</MudText>
                </MudNavLink>
                <MudNavLink Href="/monitoring/violations" Icon="@Icons.Material.Filled.Report">
                    <MudText Class="nav-text">المخالفات</MudText>
                </MudNavLink>
                <MudNavLink Href="/monitoring/actions" Icon="@Icons.Material.Filled.Build">
                    <MudText Class="nav-text">الإجراءات التصحيحية</MudText>
                </MudNavLink>
            </MudNavGroup>
            
            <!-- قطاع الشؤون الإدارية والمالية -->
            <MudNavGroup Text="الشؤون الإدارية والمالية" Icon="@Icons.Material.Filled.Business" IconColor="Color.Secondary">
                <MudNavLink Href="/admin-finance" Icon="@Icons.Material.Filled.Dashboard">
                    <MudText Class="nav-text">نظرة عامة</MudText>
                </MudNavLink>
                <MudNavLink Href="/admin-finance/hr" Icon="@Icons.Material.Filled.People">
                    <MudText Class="nav-text">الموارد البشرية</MudText>
                </MudNavLink>
                <MudNavLink Href="/admin-finance/budget" Icon="@Icons.Material.Filled.AccountBalance">
                    <MudText Class="nav-text">الميزانيات</MudText>
                </MudNavLink>
                <MudNavLink Href="/admin-finance/procurement" Icon="@Icons.Material.Filled.ShoppingCart">
                    <MudText Class="nav-text">المشتريات والعقود</MudText>
                </MudNavLink>
            </MudNavGroup>
            
            <MudDivider Class="my-2" />
            
            <!-- التقارير -->
            <MudNavGroup Text="التقارير" Icon="@Icons.Material.Filled.Assessment" IconColor="Color.Tertiary">
                <MudNavLink Href="/reports" Icon="@Icons.Material.Filled.Dashboard">
                    <MudText Class="nav-text">جميع التقارير</MudText>
                </MudNavLink>
                <MudNavLink Href="/reports/custom" Icon="@Icons.Material.Filled.Build">
                    <MudText Class="nav-text">تقارير مخصصة</MudText>
                </MudNavLink>
                <MudNavLink Href="/reports/scheduled" Icon="@Icons.Material.Filled.Schedule">
                    <MudText Class="nav-text">تقارير مجدولة</MudText>
                </MudNavLink>
            </MudNavGroup>
            
            <!-- الإعدادات -->
            <MudNavGroup Text="الإعدادات" Icon="@Icons.Material.Filled.Settings" IconColor="Color.Default">
                <MudNavLink Href="/settings/users" Icon="@Icons.Material.Filled.People">
                    <MudText Class="nav-text">المستخدمون</MudText>
                </MudNavLink>
                <MudNavLink Href="/settings/roles" Icon="@Icons.Material.Filled.AdminPanelSettings">
                    <MudText Class="nav-text">الأدوار والصلاحيات</MudText>
                </MudNavLink>
                <MudNavLink Href="/settings/system" Icon="@Icons.Material.Filled.Settings">
                    <MudText Class="nav-text">إعدادات النظام</MudText>
                </MudNavLink>
            </MudNavGroup>
        </MudNavMenu>
    </MudDrawer>

    <!-- شريط التطبيق العلوي -->
    <MudAppBar Elevation="1" Style="background: linear-gradient(90deg, rgba(33, 150, 243, 0.95) 0%, rgba(21, 101, 192, 0.95) 100%); backdrop-filter: blur(10px);">
        <MudIconButton Icon="@Icons.Material.Filled.Menu" Color="Color.Inherit" Edge="Edge.Start" OnClick="@ToggleDrawer" />
        
        <MudSpacer />
        
        <!-- معلومات المستخدم -->
        <MudMenu Icon="@Icons.Material.Filled.AccountCircle" Color="Color.Inherit" Direction="Direction.Bottom" OffsetY="true">
            <ChildContent>
                <MudMenuItem Icon="@Icons.Material.Filled.Person">الملف الشخصي</MudMenuItem>
                <MudMenuItem Icon="@Icons.Material.Filled.Settings">الإعدادات</MudMenuItem>
                <MudDivider />
                <MudMenuItem Icon="@Icons.Material.Filled.Logout" OnClick="@Logout">تسجيل الخروج</MudMenuItem>
            </ChildContent>
        </MudMenu>
        
        <!-- تبديل الوضع المظلم -->
        <MudToggleIconButton @bind-Toggled="@_isDarkMode"
                            Icon="@Icons.Material.Filled.LightMode" Color="@Color.Inherit" Title="الوضع الفاتح"
                            ToggledIcon="@Icons.Material.Filled.DarkMode" ToggledColor="@Color.Inherit" ToggledTitle="الوضع المظلم" />
    </MudAppBar>

    <!-- المحتوى الرئيسي -->
    <MudMainContent Class="pa-4" Style="background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%); min-height: 100vh;">
        @Body
    </MudMainContent>
</MudLayout>

<style>
    .nav-text {
        font-family: 'Cairo', 'Roboto', sans-serif;
        font-weight: 500;
    }
    
    .mud-nav-link {
        border-radius: 8px;
        margin: 2px 0;
        transition: all 0.3s ease;
    }
    
    .mud-nav-link:hover {
        background: rgba(33, 150, 243, 0.1);
        transform: translateX(-2px);
    }
    
    .mud-nav-link.active {
        background: rgba(33, 150, 243, 0.2);
        border-right: 3px solid #2196f3;
    }
    
    .mud-nav-group .mud-nav-group-header {
        border-radius: 8px;
        margin: 2px 0;
        font-weight: 600;
    }
</style>

@code {
    private bool _drawerOpen = true;
    private bool _isDarkMode = false;
    private MudThemeProvider _mudThemeProvider = null!;

    private readonly MudTheme _theme = new()
    {
        Palette = new PaletteLight()
        {
            Primary = "#2196f3",
            Secondary = "#ff9800",
            Success = "#4caf50",
            Info = "#00bcd4",
            Warning = "#ff5722",
            Error = "#f44336",
            Dark = "#424242",
            AppbarBackground = "#2196f3",
            Background = "#f5f5f5",
            DrawerBackground = "#ffffff",
            DrawerText = "rgba(0,0,0, 0.87)",
            Surface = "#ffffff"
        },
        PaletteDark = new PaletteDark()
        {
            Primary = "#90caf9",
            Secondary = "#ffb74d",
            Success = "#81c784",
            Info = "#4dd0e1",
            Warning = "#ff8a65",
            Error = "#e57373",
            AppbarBackground = "#1e1e1e",
            Background = "#121212",
            DrawerBackground = "#1e1e1e",
            DrawerText = "rgba(255,255,255, 0.87)",
            Surface = "#1e1e1e"
        },
        LayoutProperties = new LayoutProperties()
        {
            DrawerWidthLeft = "300px",
            DrawerWidthRight = "300px"
        },
        Typography = new Typography()
        {
            Default = new Default()
            {
                FontFamily = new[] { "Cairo", "Roboto", "Helvetica", "Arial", "sans-serif" }
            }
        }
    };

    private void ToggleDrawer()
    {
        _drawerOpen = !_drawerOpen;
    }

    private void Logout()
    {
        // TODO: Implement logout logic
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender && _mudThemeProvider != null)
        {
            _isDarkMode = await _mudThemeProvider.GetSystemPreference();
            StateHasChanged();
        }
    }
}
