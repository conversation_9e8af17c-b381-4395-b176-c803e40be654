{"ConnectionStrings": {"DefaultConnection": "Server=localhost;Database=EhsaeiDB;Integrated Security=true;TrustServerCertificate=true;MultipleActiveResultSets=true;"}, "JwtSettings": {"SecretKey": "EhsaeiStatisticsSystemSecretKey2024!@#$%^&*()_+", "Issuer": "EhsaeiAPI", "Audience": "EhsaeiClient", "ExpiryInHours": 24}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "Microsoft.EntityFrameworkCore": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/ehsaei-.txt", "rollingInterval": "Day", "retainedFileCountLimit": 30, "fileSizeLimitBytes": 10485760, "rollOnFileSizeLimit": true, "outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj}{NewLine}{Exception}"}}], "Enrich": ["FromLogContext"]}, "ApplicationSettings": {"Name": "نظام إحصائي", "Version": "1.0.0", "Environment": "Development", "DefaultLanguage": "ar", "SupportedLanguages": ["ar", "en"], "TimeZone": "Arabian Standard Time"}, "SecuritySettings": {"PasswordMinLength": 6, "PasswordRequireDigit": true, "PasswordRequireUppercase": false, "PasswordRequireLowercase": false, "PasswordRequireSpecialChar": false, "MaxLoginAttempts": 5, "LockoutDurationMinutes": 30, "SessionTimeoutMinutes": 1440}, "EmailSettings": {"SmtpServer": "", "SmtpPort": 587, "EnableSsl": true, "Username": "", "Password": "", "FromEmail": "<EMAIL>", "FromName": "نظام إحصائي"}, "FileUploadSettings": {"MaxFileSize": 10485760, "AllowedExtensions": [".jpg", ".jpeg", ".png", ".pdf", ".doc", ".docx", ".xls", ".xlsx"], "UploadPath": "uploads"}, "CacheSettings": {"DefaultExpirationMinutes": 30, "SlidingExpirationMinutes": 10}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Warning"}}, "AllowedHosts": "*"}