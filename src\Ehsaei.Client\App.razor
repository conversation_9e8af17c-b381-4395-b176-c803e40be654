@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.AspNetCore.Components.Routing
@using Microsoft.AspNetCore.Components.Web
@using MudBlazor

<CascadingAuthenticationState>
    <Router AppAssembly="@typeof(App).Assembly">
        <Found Context="routeData">
            <AuthorizeRouteView RouteData="@routeData" DefaultLayout="@typeof(MainLayout)">
                <NotAuthorized>
                    @if (context.User.Identity?.IsAuthenticated != true)
                    {
                        <RedirectToLogin />
                    }
                    else
                    {
                        <div class="d-flex justify-center align-center" style="height: 100vh;">
                            <MudPaper Class="pa-8 text-center" Elevation="3">
                                <MudIcon Icon="@Icons.Material.Filled.Lock" Size="Size.Large" Color="Color.Error" Class="mb-4" />
                                <MudText Typo="Typo.h5" Class="mb-4">غير مصرح لك بالوصول</MudText>
                                <MudText Typo="Typo.body1" Class="mb-4">
                                    عذراً، ليس لديك الصلاحيات اللازمة للوصول إلى هذه الصفحة.
                                </MudText>
                                <MudButton Variant="Variant.Filled" Color="Color.Primary" Href="/">
                                    العودة للصفحة الرئيسية
                                </MudButton>
                            </MudPaper>
                        </div>
                    }
                </NotAuthorized>
                <Authorizing>
                    <div class="d-flex justify-center align-center" style="height: 100vh;">
                        <MudPaper Class="pa-8 text-center" Elevation="3">
                            <MudProgressCircular Color="Color.Primary" Size="Size.Large" Indeterminate="true" Class="mb-4" />
                            <MudText Typo="Typo.h6">جاري التحقق من الصلاحيات...</MudText>
                        </MudPaper>
                    </div>
                </Authorizing>
            </AuthorizeRouteView>
            <FocusOnNavigate RouteData="@routeData" Selector="h1" />
        </Found>
        <NotFound>
            <PageTitle>الصفحة غير موجودة</PageTitle>
            <LayoutView Layout="@typeof(MainLayout)">
                <div class="d-flex justify-center align-center" style="height: 100vh;">
                    <MudPaper Class="pa-8 text-center" Elevation="3">
                        <MudIcon Icon="@Icons.Material.Filled.SearchOff" Size="Size.Large" Color="Color.Warning" Class="mb-4" />
                        <MudText Typo="Typo.h4" Class="mb-4">404</MudText>
                        <MudText Typo="Typo.h5" Class="mb-4">الصفحة غير موجودة</MudText>
                        <MudText Typo="Typo.body1" Class="mb-4">
                            عذراً، الصفحة التي تبحث عنها غير موجودة أو تم نقلها.
                        </MudText>
                        <MudButton Variant="Variant.Filled" Color="Color.Primary" Href="/">
                            العودة للصفحة الرئيسية
                        </MudButton>
                    </MudPaper>
                </div>
            </LayoutView>
        </NotFound>
    </Router>
</CascadingAuthenticationState>

<MudThemeProvider @ref="@_mudThemeProvider" @bind-IsDarkMode="@_isDarkMode" Theme="@_theme" />
<MudDialogProvider />
<MudSnackbarProvider />

@code {
    private bool _isDarkMode = false;
    private MudThemeProvider _mudThemeProvider = null!;

    private readonly MudTheme _theme = new()
    {
        Palette = new PaletteLight()
        {
            Primary = "#1976d2",
            Secondary = "#424242",
            AppbarBackground = "#1976d2",
            Background = "#f5f5f5",
            DrawerBackground = "#ffffff",
            DrawerText = "rgba(0,0,0, 0.87)",
            Success = "#4caf50"
        },
        PaletteDark = new PaletteDark()
        {
            Primary = "#90caf9",
            Secondary = "#f48fb1",
            AppbarBackground = "#1e1e1e",
            Background = "#121212",
            DrawerBackground = "#1e1e1e",
            DrawerText = "rgba(255,255,255, 0.87)",
            Success = "#81c784"
        },
        LayoutProperties = new LayoutProperties()
        {
            DrawerWidthLeft = "280px",
            DrawerWidthRight = "280px"
        },
        Typography = new Typography()
        {
            Default = new Default()
            {
                FontFamily = new[] { "Cairo", "Roboto", "Helvetica", "Arial", "sans-serif" }
            }
        }
    };

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender && _mudThemeProvider != null)
        {
            _isDarkMode = await _mudThemeProvider.GetSystemPreference();
            StateHasChanged();
        }
    }
}
