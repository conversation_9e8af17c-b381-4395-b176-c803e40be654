using Blazored.LocalStorage;

namespace Ehsaei.Client.Services;

public class LocalStorageWrapper
{
    private readonly ILocalStorageService _localStorage;

    public LocalStorageWrapper(ILocalStorageService localStorage)
    {
        _localStorage = localStorage;
    }

    public async Task<T?> GetItemAsync<T>(string key)
    {
        try
        {
            return await _localStorage.GetItemAsync<T>(key);
        }
        catch
        {
            return default;
        }
    }

    public async Task SetItemAsync<T>(string key, T value)
    {
        try
        {
            await _localStorage.SetItemAsync(key, value);
        }
        catch
        {
            // Handle error silently
        }
    }

    public async Task RemoveItemAsync(string key)
    {
        try
        {
            await _localStorage.RemoveItemAsync(key);
        }
        catch
        {
            // Handle error silently
        }
    }

    public async Task ClearAsync()
    {
        try
        {
            await _localStorage.ClearAsync();
        }
        catch
        {
            // Handle error silently
        }
    }
}
