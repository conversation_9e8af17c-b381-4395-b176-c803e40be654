using MudBlazor;

namespace Ehsaei.Client.Services;

public interface INotificationService
{
    void ShowSuccess(string message);
    void ShowError(string message);
    void ShowWarning(string message);
    void ShowInfo(string message);
}

public class NotificationService : INotificationService
{
    private readonly ISnackbar _snackbar;

    public NotificationService(ISnackbar snackbar)
    {
        _snackbar = snackbar;
    }

    public void ShowSuccess(string message)
    {
        _snackbar.Add(message, Severity.Success, config =>
        {
            config.Icon = Icons.Material.Filled.CheckCircle;
            config.VisibleStateDuration = 4000;
        });
    }

    public void ShowError(string message)
    {
        _snackbar.Add(message, Severity.Error, config =>
        {
            config.Icon = Icons.Material.Filled.Error;
            config.VisibleStateDuration = 6000;
        });
    }

    public void ShowWarning(string message)
    {
        _snackbar.Add(message, Severity.Warning, config =>
        {
            config.Icon = Icons.Material.Filled.Warning;
            config.VisibleStateDuration = 5000;
        });
    }

    public void ShowInfo(string message)
    {
        _snackbar.Add(message, Severity.Info, config =>
        {
            config.Icon = Icons.Material.Filled.Info;
            config.VisibleStateDuration = 4000;
        });
    }
}
