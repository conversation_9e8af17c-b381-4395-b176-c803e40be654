@page "/plant-sector/production"
@using MudBlazor

<PageTitle>الإنتاج الزراعي - القطاع النباتي</PageTitle>

<MudContainer MaxWidth="MaxWidth.ExtraLarge" Class="pa-4">
    <!-- العنوان والأزرار -->
    <MudPaper Class="pa-4 mb-4" Elevation="3">
        <div class="d-flex align-center justify-space-between">
            <div class="d-flex align-center">
                <MudIcon Icon="@Icons.Material.Filled.Agriculture" Size="Size.Large" Color="Color.Info" Class="me-3" />
                <div>
                    <MudText Typo="Typo.h4" Color="Color.Info" Class="fw-bold">الإنتاج الزراعي</MudText>
                    <MudText Typo="Typo.subtitle1">تسجيل ومتابعة الإنتاج الزراعي للمحاصيل</MudText>
                </div>
            </div>
            <MudButton Variant="Variant.Filled" Color="Color.Info" StartIcon="@Icons.Material.Filled.Add" OnClick="@OpenAddDialog">
                تسجيل إنتاج جديد
            </MudButton>
        </div>
    </MudPaper>

    <!-- إحصائيات سريعة -->
    <MudGrid Class="mb-4">
        <MudItem xs="12" sm="6" md="3">
            <MudCard Elevation="3" Class="pa-4" Style="background: linear-gradient(135deg, #2196f3 0%, #42a5f5 100%); color: white;">
                <MudCardContent Class="pa-0">
                    <div class="d-flex align-center justify-space-between">
                        <div>
                            <MudText Typo="Typo.h4" Class="fw-bold">@_totalProduction</MudText>
                            <MudText Typo="Typo.body2">إجمالي الإنتاج (طن)</MudText>
                        </div>
                        <MudIcon Icon="@Icons.Material.Filled.Scale" Size="Size.Large" />
                    </div>
                </MudCardContent>
            </MudCard>
        </MudItem>

        <MudItem xs="12" sm="6" md="3">
            <MudCard Elevation="3" Class="pa-4" Style="background: linear-gradient(135deg, #4caf50 0%, #66bb6a 100%); color: white;">
                <MudCardContent Class="pa-0">
                    <div class="d-flex align-center justify-space-between">
                        <div>
                            <MudText Typo="Typo.h4" Class="fw-bold">@_averageYield</MudText>
                            <MudText Typo="Typo.body2">متوسط الإنتاجية (طن/هكتار)</MudText>
                        </div>
                        <MudIcon Icon="@Icons.Material.Filled.TrendingUp" Size="Size.Large" />
                    </div>
                </MudCardContent>
            </MudCard>
        </MudItem>

        <MudItem xs="12" sm="6" md="3">
            <MudCard Elevation="3" Class="pa-4" Style="background: linear-gradient(135deg, #ff9800 0%, #ffb74d 100%); color: white;">
                <MudCardContent Class="pa-0">
                    <div class="d-flex align-center justify-space-between">
                        <div>
                            <MudText Typo="Typo.h4" Class="fw-bold">@_harvestedCrops</MudText>
                            <MudText Typo="Typo.body2">المحاصيل المحصودة</MudText>
                        </div>
                        <MudIcon Icon="@Icons.Material.Filled.CheckCircle" Size="Size.Large" />
                    </div>
                </MudCardContent>
            </MudCard>
        </MudItem>

        <MudItem xs="12" sm="6" md="3">
            <MudCard Elevation="3" Class="pa-4" Style="background: linear-gradient(135deg, #9c27b0 0%, #ba68c8 100%); color: white;">
                <MudCardContent Class="pa-0">
                    <div class="d-flex align-center justify-space-between">
                        <div>
                            <MudText Typo="Typo.h4" Class="fw-bold">@_totalValue</MudText>
                            <MudText Typo="Typo.body2">القيمة الإجمالية (ريال)</MudText>
                        </div>
                        <MudIcon Icon="@Icons.Material.Filled.AttachMoney" Size="Size.Large" />
                    </div>
                </MudCardContent>
            </MudCard>
        </MudItem>
    </MudGrid>

    <!-- فلاتر البحث -->
    <MudPaper Class="pa-4 mb-4" Elevation="2">
        <MudGrid AlignItems="Center">
            <MudItem xs="12" sm="6" md="3">
                <MudTextField @bind-Value="_searchText" Label="البحث" Placeholder="اسم المحصول أو المزرعة"
                             Adornment="Adornment.Start" AdornmentIcon="@Icons.Material.Filled.Search"
                             Immediate="true" OnKeyUp="@SearchProduction" />
            </MudItem>
            <MudItem xs="12" sm="6" md="3">
                <MudSelect @bind-Value="_selectedCropType" Label="نوع المحصول" Clearable="true" OnSelectionChanged="@FilterByCropType">
                    <MudSelectItem Value="@string.Empty">جميع الأنواع</MudSelectItem>
                    <MudSelectItem Value="خضروات">خضروات</MudSelectItem>
                    <MudSelectItem Value="فواكه">فواكه</MudSelectItem>
                    <MudSelectItem Value="حبوب">حبوب</MudSelectItem>
                    <MudSelectItem Value="بقوليات">بقوليات</MudSelectItem>
                    <MudSelectItem Value="أعلاف">أعلاف</MudSelectItem>
                </MudSelect>
            </MudItem>
            <MudItem xs="12" sm="6" md="3">
                <MudDateRangePicker @bind-DateRange="_dateRange" Label="فترة الحصاد" />
            </MudItem>
            <MudItem xs="12" sm="6" md="3">
                <MudButton Variant="Variant.Outlined" Color="Color.Primary" FullWidth="true" 
                          StartIcon="@Icons.Material.Filled.Refresh" OnClick="@ResetFilters">
                    إعادة تعيين
                </MudButton>
            </MudItem>
        </MudGrid>
    </MudPaper>

    <!-- جدول الإنتاج -->
    <MudPaper Class="pa-4" Elevation="3">
        <MudTable Items="_filteredProduction" Hover="true" Striped="true" Dense="false" Loading="_loading"
                  FixedHeader="true" Height="600px">
            <ToolBarContent>
                <MudText Typo="Typo.h6">سجل الإنتاج (@_filteredProduction.Count)</MudText>
                <MudSpacer />
                <MudButton Variant="Variant.Text" Color="Color.Primary" StartIcon="@Icons.Material.Filled.Download">
                    تصدير Excel
                </MudButton>
            </ToolBarContent>
            <HeaderContent>
                <MudTh><MudTableSortLabel SortBy="new Func<Production, object>(x => x.CropName)">المحصول</MudTableSortLabel></MudTh>
                <MudTh><MudTableSortLabel SortBy="new Func<Production, object>(x => x.FarmName)">المزرعة</MudTableSortLabel></MudTh>
                <MudTh><MudTableSortLabel SortBy="new Func<Production, object>(x => x.HarvestDate)">تاريخ الحصاد</MudTableSortLabel></MudTh>
                <MudTh><MudTableSortLabel SortBy="new Func<Production, object>(x => x.Area)">المساحة (هكتار)</MudTableSortLabel></MudTh>
                <MudTh><MudTableSortLabel SortBy="new Func<Production, object>(x => x.Quantity)">الكمية (طن)</MudTableSortLabel></MudTh>
                <MudTh><MudTableSortLabel SortBy="new Func<Production, object>(x => x.Yield)">الإنتاجية (طن/هكتار)</MudTableSortLabel></MudTh>
                <MudTh><MudTableSortLabel SortBy="new Func<Production, object>(x => x.Quality)">الجودة</MudTableSortLabel></MudTh>
                <MudTh><MudTableSortLabel SortBy="new Func<Production, object>(x => x.TotalValue)">القيمة (ريال)</MudTableSortLabel></MudTh>
                <MudTh>الإجراءات</MudTh>
            </HeaderContent>
            <RowTemplate>
                <MudTd DataLabel="المحصول">
                    <div class="d-flex align-center">
                        <MudAvatar Color="@GetCropTypeColor(context.CropType)" Size="Size.Small" Class="me-2">
                            <MudIcon Icon="@GetCropIcon(context.CropType)" />
                        </MudAvatar>
                        <div>
                            <MudText Typo="Typo.body1" Class="fw-bold">@context.CropName</MudText>
                            <MudText Typo="Typo.caption" Color="Color.Secondary">@context.CropType</MudText>
                        </div>
                    </div>
                </MudTd>
                <MudTd DataLabel="المزرعة">@context.FarmName</MudTd>
                <MudTd DataLabel="تاريخ الحصاد">@context.HarvestDate.ToString("yyyy/MM/dd")</MudTd>
                <MudTd DataLabel="المساحة">@context.Area.ToString("F1")</MudTd>
                <MudTd DataLabel="الكمية">@context.Quantity.ToString("F1")</MudTd>
                <MudTd DataLabel="الإنتاجية">
                    <MudText Class="fw-bold" Color="@GetYieldColor(context.Yield)">@context.Yield.ToString("F2")</MudText>
                </MudTd>
                <MudTd DataLabel="الجودة">
                    <MudChip Color="@GetQualityColor(context.Quality)" Size="Size.Small">@context.Quality</MudChip>
                </MudTd>
                <MudTd DataLabel="القيمة">@context.TotalValue.ToString("N0")</MudTd>
                <MudTd DataLabel="الإجراءات">
                    <MudButtonGroup Size="Size.Small" Variant="Variant.Text">
                        <MudIconButton Icon="@Icons.Material.Filled.Visibility" Color="Color.Info" 
                                      OnClick="@(() => ViewProduction(context))" Title="عرض التفاصيل" />
                        <MudIconButton Icon="@Icons.Material.Filled.Edit" Color="Color.Warning" 
                                      OnClick="@(() => EditProduction(context))" Title="تعديل" />
                        <MudIconButton Icon="@Icons.Material.Filled.Print" Color="Color.Secondary" 
                                      OnClick="@(() => PrintCertificate(context))" Title="طباعة شهادة" />
                        <MudIconButton Icon="@Icons.Material.Filled.Delete" Color="Color.Error" 
                                      OnClick="@(() => DeleteProduction(context))" Title="حذف" />
                    </MudButtonGroup>
                </MudTd>
            </RowTemplate>
            <PagerContent>
                <MudTablePager PageSizeOptions="new int[]{10, 25, 50, 100}" />
            </PagerContent>
        </MudTable>
    </MudPaper>
</MudContainer>

<!-- حوار إضافة/تعديل الإنتاج -->
<MudDialog @bind-IsVisible="_showAddEditDialog" Options="_dialogOptions">
    <TitleContent>
        <div class="d-flex align-center">
            <MudIcon Icon="@Icons.Material.Filled.Agriculture" Class="me-3" />
            <MudText Typo="Typo.h6">@(_isEditing ? "تعديل الإنتاج" : "تسجيل إنتاج جديد")</MudText>
        </div>
    </TitleContent>
    <DialogContent>
        <MudForm @ref="_form" Model="_currentProduction">
            <MudGrid>
                <MudItem xs="12" md="6">
                    <MudTextField @bind-Value="_currentProduction.CropName" Label="اسم المحصول" Required="true" />
                </MudItem>
                <MudItem xs="12" md="6">
                    <MudSelect @bind-Value="_currentProduction.CropType" Label="نوع المحصول" Required="true">
                        <MudSelectItem Value="خضروات">خضروات</MudSelectItem>
                        <MudSelectItem Value="فواكه">فواكه</MudSelectItem>
                        <MudSelectItem Value="حبوب">حبوب</MudSelectItem>
                        <MudSelectItem Value="بقوليات">بقوليات</MudSelectItem>
                        <MudSelectItem Value="أعلاف">أعلاف</MudSelectItem>
                    </MudSelect>
                </MudItem>
                <MudItem xs="12" md="6">
                    <MudTextField @bind-Value="_currentProduction.FarmName" Label="اسم المزرعة" Required="true" />
                </MudItem>
                <MudItem xs="12" md="6">
                    <MudDatePicker @bind-Date="_currentProduction.HarvestDate" Label="تاريخ الحصاد" Required="true" />
                </MudItem>
                <MudItem xs="12" md="6">
                    <MudNumericField @bind-Value="_currentProduction.Area" Label="المساحة (هكتار)" Required="true" Min="0" />
                </MudItem>
                <MudItem xs="12" md="6">
                    <MudNumericField @bind-Value="_currentProduction.Quantity" Label="الكمية (طن)" Required="true" Min="0" />
                </MudItem>
                <MudItem xs="12" md="6">
                    <MudSelect @bind-Value="_currentProduction.Quality" Label="الجودة" Required="true">
                        <MudSelectItem Value="ممتاز">ممتاز</MudSelectItem>
                        <MudSelectItem Value="جيد جداً">جيد جداً</MudSelectItem>
                        <MudSelectItem Value="جيد">جيد</MudSelectItem>
                        <MudSelectItem Value="متوسط">متوسط</MudSelectItem>
                        <MudSelectItem Value="ضعيف">ضعيف</MudSelectItem>
                    </MudSelect>
                </MudItem>
                <MudItem xs="12" md="6">
                    <MudNumericField @bind-Value="_currentProduction.PricePerTon" Label="السعر للطن (ريال)" Required="true" Min="0" />
                </MudItem>
                <MudItem xs="12">
                    <MudTextField @bind-Value="_currentProduction.Notes" Label="ملاحظات" Lines="3" />
                </MudItem>
            </MudGrid>
        </MudForm>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="@CloseDialog">إلغاء</MudButton>
        <MudButton Color="Color.Info" Variant="Variant.Filled" OnClick="@SaveProduction">
            @(_isEditing ? "تحديث" : "حفظ")
        </MudButton>
    </DialogActions>
</MudDialog>

@code {
    // المتغيرات
    private bool _loading = false;
    private string _searchText = string.Empty;
    private string _selectedCropType = string.Empty;
    private DateRange? _dateRange = null;
    
    // الإحصائيات
    private decimal _totalProduction = 15680.5m;
    private decimal _averageYield = 12.8m;
    private int _harvestedCrops = 89;
    private decimal _totalValue = 2450000m;
    
    // البيانات
    private List<Production> _production = new();
    private List<Production> _filteredProduction = new();
    
    // الحوار
    private bool _showAddEditDialog = false;
    private bool _isEditing = false;
    private Production _currentProduction = new();
    private MudForm _form = null!;
    
    private readonly DialogOptions _dialogOptions = new()
    {
        MaxWidth = MaxWidth.Large,
        FullWidth = true,
        CloseButton = true,
        DisableBackdropClick = true
    };

    protected override async Task OnInitializedAsync()
    {
        await LoadProduction();
    }

    private async Task LoadProduction()
    {
        _loading = true;
        
        // محاكاة تحميل البيانات
        await Task.Delay(1000);
        
        _production = new List<Production>
        {
            new() { Id = 1, CropName = "طماطم الصوب", CropType = "خضروات", FarmName = "مزرعة الخير", HarvestDate = DateTime.Now.AddDays(-5), Area = 25.5m, Quantity = 320.5m, Quality = "ممتاز", PricePerTon = 3500m, Notes = "جودة عالية" },
            new() { Id = 2, CropName = "خيار أخضر", CropType = "خضروات", FarmName = "مزرعة النماء", HarvestDate = DateTime.Now.AddDays(-10), Area = 18.2m, Quantity = 210.8m, Quality = "جيد جداً", PricePerTon = 2800m, Notes = "حصاد ممتاز" },
            new() { Id = 3, CropName = "باذنجان أسود", CropType = "خضروات", FarmName = "مزرعة الأمل", HarvestDate = DateTime.Now.AddDays(-15), Area = 22.8m, Quantity = 180.5m, Quality = "جيد", PricePerTon = 2200m, Notes = "نوعية جيدة" },
            new() { Id = 4, CropName = "تمر خلاص", CropType = "فواكه", FarmName = "مزرعة النخيل", HarvestDate = DateTime.Now.AddDays(-20), Area = 45.0m, Quantity = 450.0m, Quality = "ممتاز", PricePerTon = 15000m, Notes = "تمر فاخر" },
            new() { Id = 5, CropName = "برسيم حجازي", CropType = "أعلاف", FarmName = "مزرعة الماشية", HarvestDate = DateTime.Now.AddDays(-7), Area = 35.5m, Quantity = 520.0m, Quality = "جيد", PricePerTon = 800m, Notes = "علف عالي الجودة" }
        };
        
        _filteredProduction = _production.ToList();
        _loading = false;
    }

    private void SearchProduction()
    {
        ApplyFilters();
    }

    private void FilterByCropType()
    {
        ApplyFilters();
    }

    private void ApplyFilters()
    {
        _filteredProduction = _production.Where(p =>
            (string.IsNullOrEmpty(_searchText) || p.CropName.Contains(_searchText, StringComparison.OrdinalIgnoreCase) || p.FarmName.Contains(_searchText, StringComparison.OrdinalIgnoreCase)) &&
            (string.IsNullOrEmpty(_selectedCropType) || p.CropType == _selectedCropType) &&
            (_dateRange == null || (p.HarvestDate >= _dateRange.Start && p.HarvestDate <= _dateRange.End))
        ).ToList();
    }

    private void ResetFilters()
    {
        _searchText = string.Empty;
        _selectedCropType = string.Empty;
        _dateRange = null;
        _filteredProduction = _production.ToList();
    }

    private void OpenAddDialog()
    {
        _currentProduction = new Production();
        _isEditing = false;
        _showAddEditDialog = true;
    }

    private void ViewProduction(Production production)
    {
        // TODO: Navigate to production details page
    }

    private void EditProduction(Production production)
    {
        _currentProduction = new Production
        {
            Id = production.Id,
            CropName = production.CropName,
            CropType = production.CropType,
            FarmName = production.FarmName,
            HarvestDate = production.HarvestDate,
            Area = production.Area,
            Quantity = production.Quantity,
            Quality = production.Quality,
            PricePerTon = production.PricePerTon,
            Notes = production.Notes
        };
        _isEditing = true;
        _showAddEditDialog = true;
    }

    private void PrintCertificate(Production production)
    {
        // TODO: Generate and print production certificate
    }

    private async Task DeleteProduction(Production production)
    {
        // TODO: Show confirmation dialog and delete
    }

    private void CloseDialog()
    {
        _showAddEditDialog = false;
        _currentProduction = new();
    }

    private async Task SaveProduction()
    {
        // TODO: Validate and save production
        _showAddEditDialog = false;
        await LoadProduction();
    }

    private string GetCropIcon(string type)
    {
        return type switch
        {
            "خضروات" => Icons.Material.Filled.Eco,
            "فواكه" => Icons.Material.Filled.LocalFlorist,
            "حبوب" => Icons.Material.Filled.Grain,
            "بقوليات" => Icons.Material.Filled.Grass,
            "أعلاف" => Icons.Material.Filled.Agriculture,
            _ => Icons.Material.Filled.Eco
        };
    }

    private Color GetCropTypeColor(string type)
    {
        return type switch
        {
            "خضروات" => Color.Success,
            "فواكه" => Color.Warning,
            "حبوب" => Color.Info,
            "بقوليات" => Color.Secondary,
            "أعلاف" => Color.Primary,
            _ => Color.Default
        };
    }

    private Color GetQualityColor(string quality)
    {
        return quality switch
        {
            "ممتاز" => Color.Success,
            "جيد جداً" => Color.Info,
            "جيد" => Color.Primary,
            "متوسط" => Color.Warning,
            "ضعيف" => Color.Error,
            _ => Color.Default
        };
    }

    private Color GetYieldColor(decimal yield)
    {
        return yield switch
        {
            >= 15 => Color.Success,
            >= 10 => Color.Info,
            >= 5 => Color.Warning,
            _ => Color.Error
        };
    }

    // النموذج
    public class Production
    {
        public int Id { get; set; }
        public string CropName { get; set; } = string.Empty;
        public string CropType { get; set; } = string.Empty;
        public string FarmName { get; set; } = string.Empty;
        public DateTime HarvestDate { get; set; } = DateTime.Now;
        public decimal Area { get; set; }
        public decimal Quantity { get; set; }
        public string Quality { get; set; } = string.Empty;
        public decimal PricePerTon { get; set; }
        public string Notes { get; set; } = string.Empty;
        
        public decimal Yield => Area > 0 ? Quantity / Area : 0;
        public decimal TotalValue => Quantity * PricePerTon;
    }
}
