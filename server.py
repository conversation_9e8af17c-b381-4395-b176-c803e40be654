#!/usr/bin/env python3
import http.server
import socketserver
import webbrowser
import os

PORT = 8080

class MyHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def end_headers(self):
        self.send_header('Cache-Control', 'no-cache, no-store, must-revalidate')
        self.send_header('Pragma', 'no-cache')
        self.send_header('Expires', '0')
        super().end_headers()

if __name__ == "__main__":
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    with socketserver.TCPServer(("", PORT), MyHTTPRequestHandler) as httpd:
        print(f"🚀 نظام إحصائي يعمل على: http://localhost:{PORT}")
        print(f"📂 المجلد: {os.getcwd()}")
        print("🔗 افتح المتصفح وانتقل إلى العنوان أعلاه")
        print("⏹️  اضغط Ctrl+C لإيقاف الخادم")
        
        # فتح المتصفح تلقائياً
        webbrowser.open(f'http://localhost:{PORT}')
        
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\n🛑 تم إيقاف الخادم")
            httpd.shutdown()
