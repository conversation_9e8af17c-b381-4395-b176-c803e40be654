using System.ComponentModel.DataAnnotations;

namespace Ehsaei.Shared.DTOs;

/// <summary>
/// نموذج تسجيل الدخول
/// </summary>
public class LoginRequest
{
    [Required(ErrorMessage = "اسم المستخدم مطلوب")]
    public string Username { get; set; } = string.Empty;

    [Required(ErrorMessage = "كلمة المرور مطلوبة")]
    public string Password { get; set; } = string.Empty;

    public bool RememberMe { get; set; } = false;
}

/// <summary>
/// نموذج استجابة تسجيل الدخول
/// </summary>
public class LoginResponse
{
    public bool IsSuccess { get; set; }
    public string? Token { get; set; }
    public DateTime? ExpiresAt { get; set; }
    public UserDto? User { get; set; }
    public string? Message { get; set; }
    public List<string> Errors { get; set; } = new();
}

/// <summary>
/// نموذج تسجيل مستخدم جديد
/// </summary>
public class RegisterRequest
{
    [Required(ErrorMessage = "اسم المستخدم مطلوب")]
    [StringLength(50, MinimumLength = 3, ErrorMessage = "اسم المستخدم يجب أن يكون بين 3 و 50 حرف")]
    public string Username { get; set; } = string.Empty;

    [Required(ErrorMessage = "البريد الإلكتروني مطلوب")]
    [EmailAddress(ErrorMessage = "البريد الإلكتروني غير صحيح")]
    public string Email { get; set; } = string.Empty;

    [Required(ErrorMessage = "الاسم الكامل مطلوب")]
    [StringLength(100, MinimumLength = 2, ErrorMessage = "الاسم الكامل يجب أن يكون بين 2 و 100 حرف")]
    public string FullName { get; set; } = string.Empty;

    [Required(ErrorMessage = "كلمة المرور مطلوبة")]
    [StringLength(100, MinimumLength = 6, ErrorMessage = "كلمة المرور يجب أن تكون على الأقل 6 أحرف")]
    public string Password { get; set; } = string.Empty;

    [Required(ErrorMessage = "تأكيد كلمة المرور مطلوب")]
    [Compare("Password", ErrorMessage = "كلمة المرور وتأكيدها غير متطابقتين")]
    public string ConfirmPassword { get; set; } = string.Empty;

    public string? PhoneNumber { get; set; }
    public string? Department { get; set; }
    public string? Position { get; set; }
}

/// <summary>
/// نموذج تغيير كلمة المرور
/// </summary>
public class ChangePasswordRequest
{
    [Required(ErrorMessage = "كلمة المرور الحالية مطلوبة")]
    public string CurrentPassword { get; set; } = string.Empty;

    [Required(ErrorMessage = "كلمة المرور الجديدة مطلوبة")]
    [StringLength(100, MinimumLength = 6, ErrorMessage = "كلمة المرور يجب أن تكون على الأقل 6 أحرف")]
    public string NewPassword { get; set; } = string.Empty;

    [Required(ErrorMessage = "تأكيد كلمة المرور الجديدة مطلوب")]
    [Compare("NewPassword", ErrorMessage = "كلمة المرور الجديدة وتأكيدها غير متطابقتين")]
    public string ConfirmNewPassword { get; set; } = string.Empty;
}

/// <summary>
/// نموذج المستخدم للعرض
/// </summary>
public class UserDto
{
    public int Id { get; set; }
    public string Username { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string FullName { get; set; } = string.Empty;
    public string? PhoneNumber { get; set; }
    public string? Department { get; set; }
    public string? Position { get; set; }
    public bool IsActive { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? LastLoginAt { get; set; }
    public List<string> Roles { get; set; } = new();
    public List<string> Permissions { get; set; } = new();
    public List<SectorDto> Sectors { get; set; } = new();
}

/// <summary>
/// نموذج القطاع للعرض
/// </summary>
public class SectorDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public string? Code { get; set; }
    public string? Icon { get; set; }
    public string? Color { get; set; }
    public int DisplayOrder { get; set; }
    public bool IsActive { get; set; }
    public int AchievementsCount { get; set; }
    public int UsersCount { get; set; }
}

/// <summary>
/// نموذج الدور للعرض
/// </summary>
public class RoleDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public bool IsActive { get; set; }
    public DateTime CreatedAt { get; set; }
    public List<string> Permissions { get; set; } = new();
    public int UsersCount { get; set; }
}

/// <summary>
/// نموذج الصلاحية للعرض
/// </summary>
public class PermissionDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public string Category { get; set; } = string.Empty;
    public bool IsActive { get; set; }
}

/// <summary>
/// نموذج إنجاز القطاع للعرض
/// </summary>
public class SectorAchievementDto
{
    public int Id { get; set; }
    public int SectorId { get; set; }
    public string SectorName { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string? Description { get; set; }
    public string Type { get; set; } = string.Empty;
    public decimal? Value { get; set; }
    public string? Unit { get; set; }
    public DateTime AchievementDate { get; set; }
    public string CreatedByName { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public bool IsActive { get; set; }
}

/// <summary>
/// نموذج إنشاء/تحديث إنجاز القطاع
/// </summary>
public class CreateUpdateSectorAchievementDto
{
    public int? Id { get; set; }

    [Required(ErrorMessage = "القطاع مطلوب")]
    public int SectorId { get; set; }

    [Required(ErrorMessage = "العنوان مطلوب")]
    [StringLength(200, ErrorMessage = "العنوان يجب أن يكون أقل من 200 حرف")]
    public string Title { get; set; } = string.Empty;

    [StringLength(1000, ErrorMessage = "الوصف يجب أن يكون أقل من 1000 حرف")]
    public string? Description { get; set; }

    [Required(ErrorMessage = "النوع مطلوب")]
    [StringLength(50, ErrorMessage = "النوع يجب أن يكون أقل من 50 حرف")]
    public string Type { get; set; } = string.Empty;

    public decimal? Value { get; set; }

    [StringLength(20, ErrorMessage = "الوحدة يجب أن تكون أقل من 20 حرف")]
    public string? Unit { get; set; }

    [Required(ErrorMessage = "تاريخ الإنجاز مطلوب")]
    public DateTime AchievementDate { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// نموذج الاستجابة العامة
/// </summary>
public class ApiResponse<T>
{
    public bool IsSuccess { get; set; }
    public T? Data { get; set; }
    public string? Message { get; set; }
    public List<string> Errors { get; set; } = new();
    public int? TotalCount { get; set; }
}

/// <summary>
/// نموذج الاستجابة العامة بدون بيانات
/// </summary>
public class ApiResponse : ApiResponse<object>
{
}

/// <summary>
/// نموذج الصفحات
/// </summary>
public class PagedRequest
{
    public int Page { get; set; } = 1;
    public int PageSize { get; set; } = 10;
    public string? SearchTerm { get; set; }
    public string? SortBy { get; set; }
    public bool SortDescending { get; set; } = false;
}

/// <summary>
/// نموذج النتائج المقسمة على صفحات
/// </summary>
public class PagedResult<T>
{
    public List<T> Items { get; set; } = new();
    public int TotalCount { get; set; }
    public int Page { get; set; }
    public int PageSize { get; set; }
    public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);
    public bool HasPreviousPage => Page > 1;
    public bool HasNextPage => Page < TotalPages;
}
