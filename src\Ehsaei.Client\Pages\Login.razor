@page "/login"
@using MudBlazor
@using Ehsaei.Shared.DTOs
@using Ehsaei.Client.Services
@inject IAuthService AuthService
@inject INotificationService NotificationService
@inject NavigationManager Navigation
@layout EmptyLayout

<PageTitle>تسجيل الدخول - نظام إحصائي</PageTitle>

<div class="login-container">
    <div class="login-background"></div>
    
    <MudContainer MaxWidth="MaxWidth.Small" Class="login-content">
        <MudPaper Class="login-card pa-8" Elevation="10">
            <!-- الشعار والعنوان -->
            <div class="text-center mb-6">
                <MudIcon Icon="@Icons.Material.Filled.Analytics" Size="Size.Large" Color="Color.Primary" Class="mb-4" Style="font-size: 4rem;" />
                <MudText Typo="Typo.h4" Color="Color.Primary" Class="fw-bold mb-2">نظام إحصائي</MudText>
                <MudText Typo="Typo.subtitle1" Color="Color.Secondary">وزارة الزراعة والثروة السمكية</MudText>
                <MudDivider Class="my-4" />
                <MudText Typo="Typo.h6" Color="Color.Default">تسجيل الدخول</MudText>
            </div>

            <!-- نموذج تسجيل الدخول -->
            <MudForm @ref="_form" Model="_loginRequest" @onsubmit="HandleLogin">
                <MudGrid>
                    <MudItem xs="12">
                        <MudTextField @bind-Value="_loginRequest.Username"
                                    Label="اسم المستخدم"
                                    Variant="Variant.Outlined"
                                    Adornment="Adornment.Start"
                                    AdornmentIcon="@Icons.Material.Filled.Person"
                                    Required="true"
                                    RequiredError="اسم المستخدم مطلوب"
                                    Class="mb-4" />
                    </MudItem>
                    
                    <MudItem xs="12">
                        <MudTextField @bind-Value="_loginRequest.Password"
                                    Label="كلمة المرور"
                                    Variant="Variant.Outlined"
                                    InputType="@_passwordInputType"
                                    Adornment="Adornment.End"
                                    AdornmentIcon="@_passwordIcon"
                                    OnAdornmentClick="TogglePasswordVisibility"
                                    AdornmentAriaLabel="إظهار كلمة المرور"
                                    Required="true"
                                    RequiredError="كلمة المرور مطلوبة"
                                    Class="mb-4" />
                    </MudItem>
                    
                    <MudItem xs="12">
                        <MudCheckBox @bind-Checked="_loginRequest.RememberMe" 
                                   Label="تذكرني" 
                                   Color="Color.Primary" 
                                   Class="mb-4" />
                    </MudItem>
                    
                    <MudItem xs="12">
                        <MudButton ButtonType="ButtonType.Submit"
                                 Variant="Variant.Filled"
                                 Color="Color.Primary"
                                 FullWidth="true"
                                 Size="Size.Large"
                                 StartIcon="@Icons.Material.Filled.Login"
                                 Loading="_isLoading"
                                 Class="mb-4">
                            @if (_isLoading)
                            {
                                <MudText>جاري تسجيل الدخول...</MudText>
                            }
                            else
                            {
                                <MudText>تسجيل الدخول</MudText>
                            }
                        </MudButton>
                    </MudItem>
                </MudGrid>
            </MudForm>

            <!-- روابط إضافية -->
            <div class="text-center mt-4">
                <MudLink Href="#" Color="Color.Primary" Class="me-4">نسيت كلمة المرور؟</MudLink>
                <MudLink Href="#" Color="Color.Secondary">تحتاج مساعدة؟</MudLink>
            </div>

            <!-- معلومات تجريبية -->
            <MudAlert Severity="Severity.Info" Class="mt-6">
                <MudText Typo="Typo.body2" Class="fw-bold mb-2">بيانات تجريبية للاختبار:</MudText>
                <MudText Typo="Typo.caption">اسم المستخدم: <strong>admin</strong></MudText><br />
                <MudText Typo="Typo.caption">كلمة المرور: <strong>admin</strong></MudText>
            </MudAlert>
        </MudPaper>
        
        <!-- معلومات إضافية -->
        <div class="text-center mt-4">
            <MudText Typo="Typo.caption" Color="Color.Secondary">
                © 2024 وزارة الزراعة والثروة السمكية - سلطنة عمان
            </MudText>
        </div>
    </MudContainer>
</div>

<style>
    .login-container {
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
    
    .login-background {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" stop-color="%23ffffff" stop-opacity="0.1"/><stop offset="100%" stop-color="%23ffffff" stop-opacity="0"/></radialGradient></defs><circle cx="200" cy="200" r="100" fill="url(%23a)"/><circle cx="800" cy="300" r="150" fill="url(%23a)"/><circle cx="400" cy="700" r="120" fill="url(%23a)"/><circle cx="900" cy="800" r="80" fill="url(%23a)"/></svg>') no-repeat center center;
        background-size: cover;
        opacity: 0.3;
    }
    
    .login-content {
        position: relative;
        z-index: 1;
    }
    
    .login-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        border: 1px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
    }
    
    @media (max-width: 768px) {
        .login-card {
            margin: 20px;
        }
    }
</style>

@code {
    private MudForm _form = null!;
    private LoginRequest _loginRequest = new();
    private bool _isLoading = false;
    
    // Password visibility
    private bool _isPasswordVisible = false;
    private InputType _passwordInputType = InputType.Password;
    private string _passwordIcon = Icons.Material.Filled.VisibilityOff;

    protected override async Task OnInitializedAsync()
    {
        // التحقق من تسجيل الدخول المسبق
        if (await AuthService.IsAuthenticatedAsync())
        {
            Navigation.NavigateTo("/");
        }
    }

    private async Task HandleLogin()
    {
        if (!await _form.IsValidAsync())
            return;

        _isLoading = true;
        
        try
        {
            var result = await AuthService.LoginAsync(_loginRequest);
            
            if (result.IsSuccess)
            {
                NotificationService.ShowSuccess("تم تسجيل الدخول بنجاح");
                Navigation.NavigateTo("/");
            }
            else
            {
                NotificationService.ShowError(result.Message ?? "فشل في تسجيل الدخول");
            }
        }
        catch (Exception ex)
        {
            NotificationService.ShowError("حدث خطأ أثناء تسجيل الدخول");
        }
        finally
        {
            _isLoading = false;
        }
    }

    private void TogglePasswordVisibility()
    {
        if (_isPasswordVisible)
        {
            _isPasswordVisible = false;
            _passwordInputType = InputType.Password;
            _passwordIcon = Icons.Material.Filled.VisibilityOff;
        }
        else
        {
            _isPasswordVisible = true;
            _passwordInputType = InputType.Text;
            _passwordIcon = Icons.Material.Filled.Visibility;
        }
    }
}
