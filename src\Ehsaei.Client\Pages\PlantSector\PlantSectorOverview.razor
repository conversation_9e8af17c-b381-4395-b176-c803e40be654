@page "/plant-sector"
@using MudBlazor

<PageTitle>القطاع النباتي - نظرة عامة</PageTitle>

<MudContainer MaxWidth="MaxWidth.ExtraLarge" Class="pa-4">
    <!-- العنوان الرئيسي -->
    <MudPaper Class="pa-6 mb-4" Elevation="3" Style="background: linear-gradient(135deg, rgba(76, 175, 80, 0.1) 0%, rgba(139, 195, 74, 0.1) 100%);">
        <div class="d-flex align-center">
            <MudIcon Icon="@Icons.Material.Filled.Eco" Size="Size.Large" Color="Color.Success" Class="me-4" />
            <div>
                <MudText Typo="Typo.h4" Color="Color.Success" Class="fw-bold">القطاع النباتي</MudText>
                <MudText Typo="Typo.subtitle1" Color="Color.Default">إدارة ومراقبة الإنتاج الزراعي والمحاصيل النباتية</MudText>
            </div>
        </div>
    </MudPaper>

    <!-- البطاقات الإحصائية -->
    <MudGrid Class="mb-4">
        <MudItem xs="12" sm="6" md="3">
            <MudCard Elevation="3" Class="pa-4" Style="background: linear-gradient(135deg, #4caf50 0%, #66bb6a 100%); color: white;">
                <MudCardContent Class="pa-0">
                    <div class="d-flex align-center justify-space-between">
                        <div>
                            <MudText Typo="Typo.h4" Class="fw-bold">@_totalCrops</MudText>
                            <MudText Typo="Typo.body2">إجمالي المحاصيل</MudText>
                        </div>
                        <MudIcon Icon="@Icons.Material.Filled.Grass" Size="Size.Large" />
                    </div>
                </MudCardContent>
            </MudCard>
        </MudItem>

        <MudItem xs="12" sm="6" md="3">
            <MudCard Elevation="3" Class="pa-4" Style="background: linear-gradient(135deg, #2196f3 0%, #42a5f5 100%); color: white;">
                <MudCardContent Class="pa-0">
                    <div class="d-flex align-center justify-space-between">
                        <div>
                            <MudText Typo="Typo.h4" Class="fw-bold">@_totalProduction</MudText>
                            <MudText Typo="Typo.body2">الإنتاج (طن)</MudText>
                        </div>
                        <MudIcon Icon="@Icons.Material.Filled.Agriculture" Size="Size.Large" />
                    </div>
                </MudCardContent>
            </MudCard>
        </MudItem>

        <MudItem xs="12" sm="6" md="3">
            <MudCard Elevation="3" Class="pa-4" Style="background: linear-gradient(135deg, #ff9800 0%, #ffb74d 100%); color: white;">
                <MudCardContent Class="pa-0">
                    <div class="d-flex align-center justify-space-between">
                        <div>
                            <MudText Typo="Typo.h4" Class="fw-bold">@_cultivatedArea</MudText>
                            <MudText Typo="Typo.body2">المساحة المزروعة (هكتار)</MudText>
                        </div>
                        <MudIcon Icon="@Icons.Material.Filled.Landscape" Size="Size.Large" />
                    </div>
                </MudCardContent>
            </MudCard>
        </MudItem>

        <MudItem xs="12" sm="6" md="3">
            <MudCard Elevation="3" Class="pa-4" Style="background: linear-gradient(135deg, #9c27b0 0%, #ba68c8 100%); color: white;">
                <MudCardContent Class="pa-0">
                    <div class="d-flex align-center justify-space-between">
                        <div>
                            <MudText Typo="Typo.h4" Class="fw-bold">@_activeFarms</MudText>
                            <MudText Typo="Typo.body2">المزارع النشطة</MudText>
                        </div>
                        <MudIcon Icon="@Icons.Material.Filled.Home" Size="Size.Large" />
                    </div>
                </MudCardContent>
            </MudCard>
        </MudItem>
    </MudGrid>

    <!-- الرسوم البيانية والجداول -->
    <MudGrid>
        <!-- رسم بياني للإنتاج الشهري -->
        <MudItem xs="12" md="8">
            <MudPaper Class="pa-4" Elevation="3">
                <MudText Typo="Typo.h6" Class="mb-4" Color="Color.Primary">
                    <MudIcon Icon="@Icons.Material.Filled.TrendingUp" Class="me-2" />
                    الإنتاج الشهري للمحاصيل الرئيسية
                </MudText>
                <div style="height: 400px;">
                    <!-- رسم بياني بديل باستخدام MudBlazor -->
                    <div class="d-flex flex-column h-100">
                        <MudText Typo="Typo.h6" Class="mb-4">الإنتاج الشهري للمحاصيل الرئيسية (طن)</MudText>

                        @foreach (var item in _monthlyProduction)
                        {
                            <div class="mb-3">
                                <MudText Typo="Typo.body1" Class="mb-2">@item.Month</MudText>
                                <MudGrid>
                                    <MudItem xs="4">
                                        <MudProgressLinear Color="Color.Success" Value="@((double)item.Tomatoes)" Max="250" Class="mb-1" />
                                        <MudText Typo="Typo.caption">الطماطم: @item.Tomatoes طن</MudText>
                                    </MudItem>
                                    <MudItem xs="4">
                                        <MudProgressLinear Color="Color.Info" Value="@((double)item.Cucumber)" Max="250" Class="mb-1" />
                                        <MudText Typo="Typo.caption">الخيار: @item.Cucumber طن</MudText>
                                    </MudItem>
                                    <MudItem xs="4">
                                        <MudProgressLinear Color="Color.Warning" Value="@((double)item.Eggplant)" Max="250" Class="mb-1" />
                                        <MudText Typo="Typo.caption">الباذنجان: @item.Eggplant طن</MudText>
                                    </MudItem>
                                </MudGrid>
                            </div>
                        }
                    </div>
                </div>
            </MudPaper>
        </MudItem>

        <!-- أهم المحاصيل -->
        <MudItem xs="12" md="4">
            <MudPaper Class="pa-4" Elevation="3">
                <MudText Typo="Typo.h6" Class="mb-4" Color="Color.Primary">
                    <MudIcon Icon="@Icons.Material.Filled.Star" Class="me-2" />
                    أهم المحاصيل
                </MudText>
                <MudList>
                    @foreach (var crop in _topCrops)
                    {
                        <MudListItem>
                            <div class="d-flex align-center justify-space-between w-100">
                                <div class="d-flex align-center">
                                    <MudAvatar Color="Color.Success" Size="Size.Small" Class="me-3">
                                        <MudIcon Icon="@crop.Icon" />
                                    </MudAvatar>
                                    <div>
                                        <MudText Typo="Typo.body1" Class="fw-bold">@crop.Name</MudText>
                                        <MudText Typo="Typo.caption" Color="Color.Secondary">@crop.Area هكتار</MudText>
                                    </div>
                                </div>
                                <MudChip Color="Color.Success" Size="Size.Small">@crop.Production طن</MudChip>
                            </div>
                        </MudListItem>
                        <MudDivider />
                    }
                </MudList>
            </MudPaper>
        </MudItem>

        <!-- جدول المحاصيل الحديثة -->
        <MudItem xs="12">
            <MudPaper Class="pa-4" Elevation="3">
                <div class="d-flex align-center justify-space-between mb-4">
                    <MudText Typo="Typo.h6" Color="Color.Primary">
                        <MudIcon Icon="@Icons.Material.Filled.TableChart" Class="me-2" />
                        سجل المحاصيل الحديثة
                    </MudText>
                    <MudButton Variant="Variant.Filled" Color="Color.Success" StartIcon="@Icons.Material.Filled.Add" Href="/plant-sector/crops">
                        إضافة محصول جديد
                    </MudButton>
                </div>
                
                <MudTable Items="_recentCrops" Hover="true" Striped="true" Dense="true">
                    <HeaderContent>
                        <MudTh>اسم المحصول</MudTh>
                        <MudTh>النوع</MudTh>
                        <MudTh>المساحة (هكتار)</MudTh>
                        <MudTh>الإنتاج المتوقع (طن)</MudTh>
                        <MudTh>تاريخ الزراعة</MudTh>
                        <MudTh>الحالة</MudTh>
                        <MudTh>الإجراءات</MudTh>
                    </HeaderContent>
                    <RowTemplate>
                        <MudTd DataLabel="اسم المحصول">@context.Name</MudTd>
                        <MudTd DataLabel="النوع">@context.Type</MudTd>
                        <MudTd DataLabel="المساحة">@context.Area</MudTd>
                        <MudTd DataLabel="الإنتاج المتوقع">@context.ExpectedProduction</MudTd>
                        <MudTd DataLabel="تاريخ الزراعة">@context.PlantingDate.ToString("yyyy/MM/dd")</MudTd>
                        <MudTd DataLabel="الحالة">
                            <MudChip Color="@GetStatusColor(context.Status)" Size="Size.Small">@context.Status</MudChip>
                        </MudTd>
                        <MudTd DataLabel="الإجراءات">
                            <MudButtonGroup Size="Size.Small" Variant="Variant.Text">
                                <MudIconButton Icon="@Icons.Material.Filled.Visibility" Color="Color.Info" Size="Size.Small" />
                                <MudIconButton Icon="@Icons.Material.Filled.Edit" Color="Color.Warning" Size="Size.Small" />
                                <MudIconButton Icon="@Icons.Material.Filled.Delete" Color="Color.Error" Size="Size.Small" />
                            </MudButtonGroup>
                        </MudTd>
                    </RowTemplate>
                </MudTable>
            </MudPaper>
        </MudItem>
    </MudGrid>

    <!-- أزرار الإجراءات السريعة -->
    <MudPaper Class="pa-4 mt-4" Elevation="3">
        <MudText Typo="Typo.h6" Class="mb-4" Color="Color.Primary">الإجراءات السريعة</MudText>
        <MudGrid>
            <MudItem xs="12" sm="6" md="3">
                <MudButton Variant="Variant.Filled" Color="Color.Success" FullWidth="true" 
                          StartIcon="@Icons.Material.Filled.Add" Href="/plant-sector/crops">
                    إضافة محصول جديد
                </MudButton>
            </MudItem>
            <MudItem xs="12" sm="6" md="3">
                <MudButton Variant="Variant.Filled" Color="Color.Info" FullWidth="true" 
                          StartIcon="@Icons.Material.Filled.Assessment" Href="/plant-sector/production">
                    تسجيل الإنتاج
                </MudButton>
            </MudItem>
            <MudItem xs="12" sm="6" md="3">
                <MudButton Variant="Variant.Filled" Color="Color.Warning" FullWidth="true" 
                          StartIcon="@Icons.Material.Filled.BugReport" Href="/plant-sector/diseases">
                    تسجيل آفة/مرض
                </MudButton>
            </MudItem>
            <MudItem xs="12" sm="6" md="3">
                <MudButton Variant="Variant.Filled" Color="Color.Secondary" FullWidth="true" 
                          StartIcon="@Icons.Material.Filled.Print" Href="/reports">
                    طباعة التقارير
                </MudButton>
            </MudItem>
        </MudGrid>
    </MudPaper>
</MudContainer>

@code {
    // البيانات الإحصائية
    private int _totalCrops = 1247;
    private decimal _totalProduction = 15680.5m;
    private decimal _cultivatedArea = 2340.8m;
    private int _activeFarms = 156;

    // بيانات الإنتاج الشهري
    private List<MonthlyProduction> _monthlyProduction = new();

    // أهم المحاصيل
    private List<TopCrop> _topCrops = new();

    // المحاصيل الحديثة
    private List<RecentCrop> _recentCrops = new();

    protected override async Task OnInitializedAsync()
    {
        await LoadData();
    }

    private async Task LoadData()
    {
        // تحميل بيانات الإنتاج الشهري
        _monthlyProduction = new List<MonthlyProduction>
        {
            new() { Month = "يناير", Tomatoes = 120, Cucumber = 80, Eggplant = 60 },
            new() { Month = "فبراير", Tomatoes = 150, Cucumber = 95, Eggplant = 75 },
            new() { Month = "مارس", Tomatoes = 180, Cucumber = 110, Eggplant = 90 },
            new() { Month = "أبريل", Tomatoes = 200, Cucumber = 130, Eggplant = 100 },
            new() { Month = "مايو", Tomatoes = 220, Cucumber = 140, Eggplant = 110 },
            new() { Month = "يونيو", Tomatoes = 190, Cucumber = 120, Eggplant = 95 }
        };

        // تحميل أهم المحاصيل
        _topCrops = new List<TopCrop>
        {
            new() { Name = "الطماطم", Area = 450.5m, Production = 2800.0m, Icon = Icons.Material.Filled.LocalFlorist },
            new() { Name = "الخيار", Area = 320.8m, Production = 1950.0m, Icon = Icons.Material.Filled.Eco },
            new() { Name = "الباذنجان", Area = 280.2m, Production = 1680.0m, Icon = Icons.Material.Filled.Grass },
            new() { Name = "الفلفل", Area = 210.5m, Production = 1260.0m, Icon = Icons.Material.Filled.LocalFlorist },
            new() { Name = "البصل", Area = 180.3m, Production = 1080.0m, Icon = Icons.Material.Filled.Eco }
        };

        // تحميل المحاصيل الحديثة
        _recentCrops = new List<RecentCrop>
        {
            new() { Name = "طماطم الصوب", Type = "خضروات", Area = 25.5m, ExpectedProduction = 180.0m, PlantingDate = DateTime.Now.AddDays(-15), Status = "نمو" },
            new() { Name = "خيار هجين", Type = "خضروات", Area = 18.2m, ExpectedProduction = 120.0m, PlantingDate = DateTime.Now.AddDays(-20), Status = "إزهار" },
            new() { Name = "باذنجان أسود", Type = "خضروات", Area = 22.8m, ExpectedProduction = 150.0m, PlantingDate = DateTime.Now.AddDays(-25), Status = "نضج" },
            new() { Name = "فلفل حلو", Type = "خضروات", Area = 15.5m, ExpectedProduction = 95.0m, PlantingDate = DateTime.Now.AddDays(-30), Status = "حصاد" },
            new() { Name = "بصل أحمر", Type = "خضروات", Area = 30.0m, ExpectedProduction = 200.0m, PlantingDate = DateTime.Now.AddDays(-45), Status = "نمو" }
        };
    }

    private Color GetStatusColor(string status)
    {
        return status switch
        {
            "نمو" => Color.Info,
            "إزهار" => Color.Warning,
            "نضج" => Color.Success,
            "حصاد" => Color.Primary,
            _ => Color.Default
        };
    }

    // النماذج
    public class MonthlyProduction
    {
        public string Month { get; set; } = string.Empty;
        public decimal Tomatoes { get; set; }
        public decimal Cucumber { get; set; }
        public decimal Eggplant { get; set; }
    }

    public class TopCrop
    {
        public string Name { get; set; } = string.Empty;
        public decimal Area { get; set; }
        public decimal Production { get; set; }
        public string Icon { get; set; } = string.Empty;
    }

    public class RecentCrop
    {
        public string Name { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public decimal Area { get; set; }
        public decimal ExpectedProduction { get; set; }
        public DateTime PlantingDate { get; set; }
        public string Status { get; set; } = string.Empty;
    }
}
