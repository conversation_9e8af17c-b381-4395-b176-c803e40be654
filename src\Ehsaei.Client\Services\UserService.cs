using Ehsaei.Shared.DTOs;

namespace Ehsaei.Client.Services;

public interface IUserService
{
    Task<List<UserDto>> GetUsersAsync();
    Task<UserDto?> GetUserByIdAsync(int id);
    Task<UserDto?> CreateUserAsync(RegisterRequest request);
    Task<UserDto?> UpdateUserAsync(int id, UserDto user);
    Task<bool> DeleteUserAsync(int id);
}

public class UserService : IUserService
{
    private readonly IApiService _apiService;

    public UserService(IApiService apiService)
    {
        _apiService = apiService;
    }

    public async Task<List<UserDto>> GetUsersAsync()
    {
        // محاكاة البيانات للاختبار
        await Task.Delay(500);
        
        return new List<UserDto>
        {
            new()
            {
                Id = 1,
                Username = "admin",
                Email = "<EMAIL>",
                FullName = "مدير النظام",
                Department = "تقنية المعلومات",
                Position = "مدير النظام",
                IsActive = true,
                CreatedAt = DateTime.Now.AddYears(-1),
                Roles = new List<string> { "مدير النظام" }
            },
            new()
            {
                Id = 2,
                Username = "manager",
                Email = "<EMAIL>",
                FullName = "مدير القطاع النباتي",
                Department = "القطاع النباتي",
                Position = "مدير قطاع",
                IsActive = true,
                CreatedAt = DateTime.Now.AddMonths(-6),
                Roles = new List<string> { "مدير قطاع" }
            }
        };
    }

    public async Task<UserDto?> GetUserByIdAsync(int id)
    {
        var users = await GetUsersAsync();
        return users.FirstOrDefault(u => u.Id == id);
    }

    public async Task<UserDto?> CreateUserAsync(RegisterRequest request)
    {
        // محاكاة إنشاء مستخدم جديد
        await Task.Delay(1000);
        
        return new UserDto
        {
            Id = new Random().Next(100, 999),
            Username = request.Username,
            Email = request.Email,
            FullName = request.FullName,
            Department = request.Department,
            Position = request.Position,
            IsActive = true,
            CreatedAt = DateTime.Now,
            Roles = new List<string> { "مستخدم" }
        };
    }

    public async Task<UserDto?> UpdateUserAsync(int id, UserDto user)
    {
        // محاكاة تحديث المستخدم
        await Task.Delay(800);
        return user;
    }

    public async Task<bool> DeleteUserAsync(int id)
    {
        // محاكاة حذف المستخدم
        await Task.Delay(500);
        return true;
    }
}
