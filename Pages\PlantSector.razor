@page "/plant-sector"
@using MudBlazor

<PageTitle>القطاع النباتي - نظام إحصائي</PageTitle>

<MudContainer MaxWidth="MaxWidth.ExtraLarge" Class="pa-4">
    <!-- العنوان الرئيسي -->
    <MudPaper Class="pa-6 mb-4" Elevation="3" Style="background: linear-gradient(135deg, rgba(76, 175, 80, 0.1) 0%, rgba(139, 195, 74, 0.1) 100%);">
        <div class="d-flex align-center">
            <MudIcon Icon="@Icons.Material.Filled.Eco" Size="Size.Large" Color="Color.Success" Class="me-4" />
            <div>
                <MudText Typo="Typo.h4" Color="Color.Success" Class="fw-bold">القطاع النباتي</MudText>
                <MudText Typo="Typo.subtitle1" Color="Color.Default">إدارة ومراقبة الإنتاج الزراعي والمحاصيل النباتية</MudText>
            </div>
        </div>
    </MudPaper>

    <!-- البطاقات الإحصائية -->
    <MudGrid Class="mb-4">
        <MudItem xs="12" sm="6" md="3">
            <MudCard Elevation="3" Class="pa-4" Style="background: linear-gradient(135deg, #4caf50 0%, #66bb6a 100%); color: white;">
                <MudCardContent Class="pa-0">
                    <div class="d-flex align-center justify-space-between">
                        <div>
                            <MudText Typo="Typo.h4" Class="fw-bold">1,247</MudText>
                            <MudText Typo="Typo.body2">إجمالي المحاصيل</MudText>
                        </div>
                        <MudIcon Icon="@Icons.Material.Filled.Grass" Size="Size.Large" />
                    </div>
                </MudCardContent>
            </MudCard>
        </MudItem>

        <MudItem xs="12" sm="6" md="3">
            <MudCard Elevation="3" Class="pa-4" Style="background: linear-gradient(135deg, #2196f3 0%, #42a5f5 100%); color: white;">
                <MudCardContent Class="pa-0">
                    <div class="d-flex align-center justify-space-between">
                        <div>
                            <MudText Typo="Typo.h4" Class="fw-bold">15,680</MudText>
                            <MudText Typo="Typo.body2">الإنتاج (طن)</MudText>
                        </div>
                        <MudIcon Icon="@Icons.Material.Filled.Agriculture" Size="Size.Large" />
                    </div>
                </MudCardContent>
            </MudCard>
        </MudItem>

        <MudItem xs="12" sm="6" md="3">
            <MudCard Elevation="3" Class="pa-4" Style="background: linear-gradient(135deg, #ff9800 0%, #ffb74d 100%); color: white;">
                <MudCardContent Class="pa-0">
                    <div class="d-flex align-center justify-space-between">
                        <div>
                            <MudText Typo="Typo.h4" Class="fw-bold">2,341</MudText>
                            <MudText Typo="Typo.body2">المساحة المزروعة (هكتار)</MudText>
                        </div>
                        <MudIcon Icon="@Icons.Material.Filled.Landscape" Size="Size.Large" />
                    </div>
                </MudCardContent>
            </MudCard>
        </MudItem>

        <MudItem xs="12" sm="6" md="3">
            <MudCard Elevation="3" Class="pa-4" Style="background: linear-gradient(135deg, #9c27b0 0%, #ba68c8 100%); color: white;">
                <MudCardContent Class="pa-0">
                    <div class="d-flex align-center justify-space-between">
                        <div>
                            <MudText Typo="Typo.h4" Class="fw-bold">89</MudText>
                            <MudText Typo="Typo.body2">المزارع النشطة</MudText>
                        </div>
                        <MudIcon Icon="@Icons.Material.Filled.Home" Size="Size.Large" />
                    </div>
                </MudCardContent>
            </MudCard>
        </MudItem>
    </MudGrid>

    <!-- جدول المحاصيل -->
    <MudPaper Class="pa-4 mb-4" Elevation="3">
        <div class="d-flex align-center justify-space-between mb-4">
            <MudText Typo="Typo.h6" Color="Color.Primary">
                <MudIcon Icon="@Icons.Material.Filled.TableChart" Class="me-2" />
                سجل المحاصيل الحديثة
            </MudText>
            <MudButton Variant="Variant.Filled" Color="Color.Success" StartIcon="@Icons.Material.Filled.Add" OnClick="@OpenAddDialog">
                إضافة محصول جديد
            </MudButton>
        </div>
        
        <MudTable Items="_crops" Hover="true" Striped="true" Dense="true">
            <HeaderContent>
                <MudTh>اسم المحصول</MudTh>
                <MudTh>النوع</MudTh>
                <MudTh>المساحة (هكتار)</MudTh>
                <MudTh>الإنتاج المتوقع (طن)</MudTh>
                <MudTh>تاريخ الزراعة</MudTh>
                <MudTh>الحالة</MudTh>
                <MudTh>الإجراءات</MudTh>
            </HeaderContent>
            <RowTemplate>
                <MudTd DataLabel="اسم المحصول">
                    <div class="d-flex align-center">
                        <MudAvatar Color="Color.Success" Size="Size.Small" Class="me-2">
                            <MudIcon Icon="@Icons.Material.Filled.Eco" />
                        </MudAvatar>
                        <div>
                            <MudText Typo="Typo.body1" Class="fw-bold">@context.Name</MudText>
                            <MudText Typo="Typo.caption" Color="Color.Secondary">@context.FarmName</MudText>
                        </div>
                    </div>
                </MudTd>
                <MudTd DataLabel="النوع">
                    <MudChip Color="Color.Success" Size="Size.Small">@context.Type</MudChip>
                </MudTd>
                <MudTd DataLabel="المساحة">@context.Area.ToString("F1")</MudTd>
                <MudTd DataLabel="الإنتاج المتوقع">@context.ExpectedProduction.ToString("F1")</MudTd>
                <MudTd DataLabel="تاريخ الزراعة">@context.PlantingDate.ToString("yyyy/MM/dd")</MudTd>
                <MudTd DataLabel="الحالة">
                    <MudChip Color="@GetStatusColor(context.Status)" Size="Size.Small">@context.Status</MudChip>
                </MudTd>
                <MudTd DataLabel="الإجراءات">
                    <MudButtonGroup Size="Size.Small" Variant="Variant.Text">
                        <MudIconButton Icon="@Icons.Material.Filled.Visibility" Color="Color.Info" Size="Size.Small" OnClick="@(() => ViewCrop(context))" />
                        <MudIconButton Icon="@Icons.Material.Filled.Edit" Color="Color.Warning" Size="Size.Small" OnClick="@(() => EditCrop(context))" />
                        <MudIconButton Icon="@Icons.Material.Filled.Delete" Color="Color.Error" Size="Size.Small" OnClick="@(() => DeleteCrop(context))" />
                    </MudButtonGroup>
                </MudTd>
            </RowTemplate>
        </MudTable>
    </MudPaper>

    <!-- أزرار الإجراءات السريعة -->
    <MudPaper Class="pa-4" Elevation="3">
        <MudText Typo="Typo.h6" Class="mb-4" Color="Color.Primary">الإجراءات السريعة</MudText>
        <MudGrid>
            <MudItem xs="12" sm="6" md="3">
                <MudButton Variant="Variant.Filled" Color="Color.Success" FullWidth="true" 
                          StartIcon="@Icons.Material.Filled.Add" OnClick="@OpenAddDialog">
                    إضافة محصول جديد
                </MudButton>
            </MudItem>
            <MudItem xs="12" sm="6" md="3">
                <MudButton Variant="Variant.Filled" Color="Color.Info" FullWidth="true" 
                          StartIcon="@Icons.Material.Filled.Assessment">
                    تسجيل الإنتاج
                </MudButton>
            </MudItem>
            <MudItem xs="12" sm="6" md="3">
                <MudButton Variant="Variant.Filled" Color="Color.Warning" FullWidth="true" 
                          StartIcon="@Icons.Material.Filled.BugReport">
                    تسجيل آفة/مرض
                </MudButton>
            </MudItem>
            <MudItem xs="12" sm="6" md="3">
                <MudButton Variant="Variant.Filled" Color="Color.Secondary" FullWidth="true" 
                          StartIcon="@Icons.Material.Filled.Print">
                    طباعة التقارير
                </MudButton>
            </MudItem>
        </MudGrid>
    </MudPaper>
</MudContainer>

<!-- حوار إضافة محصول -->
<MudDialog @bind-IsVisible="_showAddDialog" Options="_dialogOptions">
    <TitleContent>
        <MudText Typo="Typo.h6">
            <MudIcon Icon="@Icons.Material.Filled.Add" Class="me-3" />
            إضافة محصول جديد
        </MudText>
    </TitleContent>
    <DialogContent>
        <MudGrid>
            <MudItem xs="12" md="6">
                <MudTextField @bind-Value="_newCrop.Name" Label="اسم المحصول" Required="true" />
            </MudItem>
            <MudItem xs="12" md="6">
                <MudSelect @bind-Value="_newCrop.Type" Label="نوع المحصول" Required="true">
                    <MudSelectItem Value="خضروات">خضروات</MudSelectItem>
                    <MudSelectItem Value="فواكه">فواكه</MudSelectItem>
                    <MudSelectItem Value="حبوب">حبوب</MudSelectItem>
                    <MudSelectItem Value="بقوليات">بقوليات</MudSelectItem>
                    <MudSelectItem Value="أعلاف">أعلاف</MudSelectItem>
                </MudSelect>
            </MudItem>
            <MudItem xs="12" md="6">
                <MudTextField @bind-Value="_newCrop.FarmName" Label="اسم المزرعة" Required="true" />
            </MudItem>
            <MudItem xs="12" md="6">
                <MudNumericField @bind-Value="_newCrop.Area" Label="المساحة (هكتار)" Required="true" Min="0" />
            </MudItem>
            <MudItem xs="12" md="6">
                <MudNumericField @bind-Value="_newCrop.ExpectedProduction" Label="الإنتاج المتوقع (طن)" Required="true" Min="0" />
            </MudItem>
            <MudItem xs="12" md="6">
                <MudDatePicker @bind-Date="_newCrop.PlantingDate" Label="تاريخ الزراعة" Required="true" />
            </MudItem>
        </MudGrid>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="@CloseAddDialog">إلغاء</MudButton>
        <MudButton Color="Color.Success" Variant="Variant.Filled" OnClick="@AddCrop">حفظ</MudButton>
    </DialogActions>
</MudDialog>

@code {
    private List<Crop> _crops = new();
    private bool _showAddDialog = false;
    private Crop _newCrop = new();
    
    private readonly DialogOptions _dialogOptions = new()
    {
        MaxWidth = MaxWidth.Medium,
        FullWidth = true
    };

    protected override void OnInitialized()
    {
        LoadSampleData();
    }

    private void LoadSampleData()
    {
        _crops = new List<Crop>
        {
            new() { Id = 1, Name = "طماطم الصوب", Type = "خضروات", FarmName = "مزرعة الخير", Area = 25.5m, ExpectedProduction = 180.0m, PlantingDate = DateTime.Now.AddDays(-15), Status = "نمو" },
            new() { Id = 2, Name = "خيار هجين", Type = "خضروات", FarmName = "مزرعة النماء", Area = 18.2m, ExpectedProduction = 120.0m, PlantingDate = DateTime.Now.AddDays(-20), Status = "إزهار" },
            new() { Id = 3, Name = "باذنجان أسود", Type = "خضروات", FarmName = "مزرعة الأمل", Area = 22.8m, ExpectedProduction = 150.0m, PlantingDate = DateTime.Now.AddDays(-25), Status = "نضج" },
            new() { Id = 4, Name = "فلفل حلو", Type = "خضروات", FarmName = "مزرعة الخضار", Area = 15.5m, ExpectedProduction = 95.0m, PlantingDate = DateTime.Now.AddDays(-30), Status = "حصاد" },
            new() { Id = 5, Name = "بصل أحمر", Type = "خضروات", FarmName = "مزرعة البصل", Area = 30.0m, ExpectedProduction = 200.0m, PlantingDate = DateTime.Now.AddDays(-45), Status = "نمو" }
        };
    }

    private Color GetStatusColor(string status)
    {
        return status switch
        {
            "نمو" => Color.Info,
            "إزهار" => Color.Warning,
            "نضج" => Color.Success,
            "حصاد" => Color.Primary,
            _ => Color.Default
        };
    }

    private void OpenAddDialog()
    {
        _newCrop = new Crop { PlantingDate = DateTime.Now };
        _showAddDialog = true;
    }

    private void CloseAddDialog()
    {
        _showAddDialog = false;
        _newCrop = new();
    }

    private void AddCrop()
    {
        _newCrop.Id = _crops.Count + 1;
        _newCrop.Status = "زراعة";
        _crops.Add(_newCrop);
        CloseAddDialog();
    }

    private void ViewCrop(Crop crop)
    {
        // TODO: عرض تفاصيل المحصول
    }

    private void EditCrop(Crop crop)
    {
        // TODO: تعديل المحصول
    }

    private void DeleteCrop(Crop crop)
    {
        _crops.Remove(crop);
    }

    public class Crop
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public string FarmName { get; set; } = string.Empty;
        public decimal Area { get; set; }
        public decimal ExpectedProduction { get; set; }
        public DateTime PlantingDate { get; set; } = DateTime.Now;
        public string Status { get; set; } = "زراعة";
    }
}
