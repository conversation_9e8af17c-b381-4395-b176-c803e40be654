@page "/"
@using MudBlazor

<PageTitle>لوحة التحكم - نظام إحصائي</PageTitle>

<MudContainer MaxWidth="MaxWidth.ExtraLarge" Class="pa-4">
    <!-- ترحيب -->
    <MudPaper Class="pa-6 mb-4" Elevation="3" Style="background: linear-gradient(135deg, rgba(33, 150, 243, 0.1) 0%, rgba(21, 101, 192, 0.1) 100%);">
        <div class="d-flex align-center justify-space-between">
            <div>
                <MudText Typo="Typo.h4" Color="Color.Primary" Class="fw-bold">مرحباً بك في نظام إحصائي</MudText>
                <MudText Typo="Typo.subtitle1" Color="Color.Secondary">وزارة الزراعة والثروة السمكية - سلطنة عمان</MudText>
                <MudText Typo="Typo.body2" Color="Color.Secondary" Class="mt-2">آخر تحديث: @DateTime.Now.ToString("yyyy/MM/dd - HH:mm")</MudText>
            </div>
            <MudIcon Icon="@Icons.Material.Filled.Dashboard" Size="Size.Large" Color="Color.Primary" />
        </div>
    </MudPaper>

    <!-- الإحصائيات العامة -->
    <MudGrid Class="mb-4">
        <MudItem xs="12" sm="6" md="3">
            <MudCard Elevation="3" Class="pa-4" Style="background: linear-gradient(135deg, #4caf50 0%, #66bb6a 100%); color: white;">
                <MudCardContent Class="pa-0">
                    <div class="d-flex align-center justify-space-between">
                        <div>
                            <MudText Typo="Typo.h4" Class="fw-bold">6</MudText>
                            <MudText Typo="Typo.body2">القطاعات النشطة</MudText>
                        </div>
                        <MudIcon Icon="@Icons.Material.Filled.Category" Size="Size.Large" />
                    </div>
                </MudCardContent>
            </MudCard>
        </MudItem>

        <MudItem xs="12" sm="6" md="3">
            <MudCard Elevation="3" Class="pa-4" Style="background: linear-gradient(135deg, #2196f3 0%, #42a5f5 100%); color: white;">
                <MudCardContent Class="pa-0">
                    <div class="d-flex align-center justify-space-between">
                        <div>
                            <MudText Typo="Typo.h4" Class="fw-bold">1,247</MudText>
                            <MudText Typo="Typo.body2">إجمالي المحاصيل</MudText>
                        </div>
                        <MudIcon Icon="@Icons.Material.Filled.Grass" Size="Size.Large" />
                    </div>
                </MudCardContent>
            </MudCard>
        </MudItem>

        <MudItem xs="12" sm="6" md="3">
            <MudCard Elevation="3" Class="pa-4" Style="background: linear-gradient(135deg, #ff9800 0%, #ffb74d 100%); color: white;">
                <MudCardContent Class="pa-0">
                    <div class="d-flex align-center justify-space-between">
                        <div>
                            <MudText Typo="Typo.h4" Class="fw-bold">15,680</MudText>
                            <MudText Typo="Typo.body2">الإنتاج الإجمالي (طن)</MudText>
                        </div>
                        <MudIcon Icon="@Icons.Material.Filled.Agriculture" Size="Size.Large" />
                    </div>
                </MudCardContent>
            </MudCard>
        </MudItem>

        <MudItem xs="12" sm="6" md="3">
            <MudCard Elevation="3" Class="pa-4" Style="background: linear-gradient(135deg, #9c27b0 0%, #ba68c8 100%); color: white;">
                <MudCardContent Class="pa-0">
                    <div class="d-flex align-center justify-space-between">
                        <div>
                            <MudText Typo="Typo.h4" Class="fw-bold">156</MudText>
                            <MudText Typo="Typo.body2">المزارع النشطة</MudText>
                        </div>
                        <MudIcon Icon="@Icons.Material.Filled.Home" Size="Size.Large" />
                    </div>
                </MudCardContent>
            </MudCard>
        </MudItem>
    </MudGrid>

    <!-- الرسوم البيانية والقطاعات -->
    <MudGrid>
        <!-- رسم بياني للإنتاج الشهري -->
        <MudItem xs="12" md="8">
            <MudPaper Class="pa-4" Elevation="3">
                <MudText Typo="Typo.h6" Class="mb-4" Color="Color.Primary">
                    <MudIcon Icon="@Icons.Material.Filled.TrendingUp" Class="me-2" />
                    الإنتاج الشهري لجميع القطاعات
                </MudText>
                <div style="height: 400px; display: flex; align-items: center; justify-content: center;">
                    <div class="text-center">
                        <MudIcon Icon="@Icons.Material.Filled.TrendingUp" Size="Size.Large" Color="Color.Primary" Class="mb-3" />
                        <MudText Typo="Typo.h6" Color="Color.Primary">الرسم البياني للإنتاج الشهري</MudText>
                        <MudText Typo="Typo.body2" Color="Color.Secondary">سيتم عرض الرسم البياني التفاعلي هنا</MudText>
                        <MudProgressLinear Color="Color.Primary" Indeterminate="true" Class="mt-3" />

                        <!-- بيانات نصية بديلة -->
                        <div class="mt-4">
                            <MudGrid>
                                <MudItem xs="4">
                                    <MudPaper Class="pa-3" Elevation="2">
                                        <MudText Typo="Typo.caption" Color="Color.Success">القطاع النباتي</MudText>
                                        <MudText Typo="Typo.h6" Color="Color.Success">1,680 طن</MudText>
                                    </MudPaper>
                                </MudItem>
                                <MudItem xs="4">
                                    <MudPaper Class="pa-3" Elevation="2">
                                        <MudText Typo="Typo.caption" Color="Color.Warning">القطاع الحيواني</MudText>
                                        <MudText Typo="Typo.h6" Color="Color.Warning">980 طن</MudText>
                                    </MudPaper>
                                </MudItem>
                                <MudItem xs="4">
                                    <MudPaper Class="pa-3" Elevation="2">
                                        <MudText Typo="Typo.caption" Color="Color.Info">قطاع المياه</MudText>
                                        <MudText Typo="Typo.h6" Color="Color.Info">490 طن</MudText>
                                    </MudPaper>
                                </MudItem>
                            </MudGrid>
                        </div>
                    </div>
                </div>
            </MudPaper>
        </MudItem>

        <!-- القطاعات السريعة -->
        <MudItem xs="12" md="4">
            <MudPaper Class="pa-4" Elevation="3">
                <MudText Typo="Typo.h6" Class="mb-4" Color="Color.Primary">
                    <MudIcon Icon="@Icons.Material.Filled.Speed" Class="me-2" />
                    الوصول السريع للقطاعات
                </MudText>
                <MudList>
                    <MudListItem Href="/plant-sector" Class="rounded mb-2" Style="background: rgba(76, 175, 80, 0.1);">
                        <div class="d-flex align-center">
                            <MudAvatar Color="Color.Success" Size="Size.Medium" Class="me-3">
                                <MudIcon Icon="@Icons.Material.Filled.Eco" />
                            </MudAvatar>
                            <div>
                                <MudText Typo="Typo.body1" Class="fw-bold">القطاع النباتي</MudText>
                                <MudText Typo="Typo.caption" Color="Color.Secondary">1,247 محصول نشط</MudText>
                            </div>
                        </div>
                    </MudListItem>

                    <MudListItem Href="/animal-sector" Class="rounded mb-2" Style="background: rgba(255, 152, 0, 0.1);">
                        <div class="d-flex align-center">
                            <MudAvatar Color="Color.Warning" Size="Size.Medium" Class="me-3">
                                <MudIcon Icon="@Icons.Material.Filled.Pets" />
                            </MudAvatar>
                            <div>
                                <MudText Typo="Typo.body1" Class="fw-bold">القطاع الحيواني</MudText>
                                <MudText Typo="Typo.caption" Color="Color.Secondary">856 رأس ماشية</MudText>
                            </div>
                        </div>
                    </MudListItem>

                    <MudListItem Href="/water-sector" Class="rounded mb-2" Style="background: rgba(33, 150, 243, 0.1);">
                        <div class="d-flex align-center">
                            <MudAvatar Color="Color.Info" Size="Size.Medium" Class="me-3">
                                <MudIcon Icon="@Icons.Material.Filled.Water" />
                            </MudAvatar>
                            <div>
                                <MudText Typo="Typo.body1" Class="fw-bold">قطاع موارد المياه</MudText>
                                <MudText Typo="Typo.caption" Color="Color.Secondary">45 مصدر مياه</MudText>
                            </div>
                        </div>
                    </MudListItem>

                    <MudListItem Href="/food-safety" Class="rounded mb-2" Style="background: rgba(244, 67, 54, 0.1);">
                        <div class="d-flex align-center">
                            <MudAvatar Color="Color.Error" Size="Size.Medium" Class="me-3">
                                <MudIcon Icon="@Icons.Material.Filled.FoodBank" />
                            </MudAvatar>
                            <div>
                                <MudText Typo="Typo.body1" Class="fw-bold">جودة وسلامة الغذاء</MudText>
                                <MudText Typo="Typo.caption" Color="Color.Secondary">234 فحص جودة</MudText>
                            </div>
                        </div>
                    </MudListItem>

                    <MudListItem Href="/monitoring" Class="rounded mb-2" Style="background: rgba(96, 125, 139, 0.1);">
                        <div class="d-flex align-center">
                            <MudAvatar Color="Color.Dark" Size="Size.Medium" Class="me-3">
                                <MudIcon Icon="@Icons.Material.Filled.Security" />
                            </MudAvatar>
                            <div>
                                <MudText Typo="Typo.body1" Class="fw-bold">قطاع الرقابة</MudText>
                                <MudText Typo="Typo.caption" Color="Color.Secondary">89 تفتيش</MudText>
                            </div>
                        </div>
                    </MudListItem>

                    <MudListItem Href="/admin-finance" Class="rounded" Style="background: rgba(156, 39, 176, 0.1);">
                        <div class="d-flex align-center">
                            <MudAvatar Color="Color.Secondary" Size="Size.Medium" Class="me-3">
                                <MudIcon Icon="@Icons.Material.Filled.Business" />
                            </MudAvatar>
                            <div>
                                <MudText Typo="Typo.body1" Class="fw-bold">الشؤون الإدارية والمالية</MudText>
                                <MudText Typo="Typo.caption" Color="Color.Secondary">67 موظف</MudText>
                            </div>
                        </div>
                    </MudListItem>
                </MudList>
            </MudPaper>
        </MudItem>

        <!-- الأنشطة الحديثة -->
        <MudItem xs="12" md="6">
            <MudPaper Class="pa-4" Elevation="3">
                <MudText Typo="Typo.h6" Class="mb-4" Color="Color.Primary">
                    <MudIcon Icon="@Icons.Material.Filled.History" Class="me-2" />
                    الأنشطة الحديثة
                </MudText>
                <MudTimeline TimelineOrientation="TimelineOrientation.Vertical">
                    @foreach (var activity in _recentActivities)
                    {
                        <MudTimelineItem Color="@activity.Color" Size="Size.Small">
                            <ItemContent>
                                <div class="d-flex align-center justify-space-between">
                                    <div>
                                        <MudText Typo="Typo.body2" Class="fw-bold">@activity.Title</MudText>
                                        <MudText Typo="Typo.caption" Color="Color.Secondary">@activity.Description</MudText>
                                    </div>
                                    <MudText Typo="Typo.caption" Color="Color.Secondary">@activity.Time</MudText>
                                </div>
                            </ItemContent>
                        </MudTimelineItem>
                    }
                </MudTimeline>
            </MudPaper>
        </MudItem>

        <!-- التقارير السريعة -->
        <MudItem xs="12" md="6">
            <MudPaper Class="pa-4" Elevation="3">
                <MudText Typo="Typo.h6" Class="mb-4" Color="Color.Primary">
                    <MudIcon Icon="@Icons.Material.Filled.Assessment" Class="me-2" />
                    التقارير السريعة
                </MudText>
                <MudGrid>
                    <MudItem xs="12" sm="6">
                        <MudButton Variant="Variant.Outlined" Color="Color.Success" FullWidth="true" 
                                  StartIcon="@Icons.Material.Filled.Eco" Class="mb-2">
                            تقرير القطاع النباتي
                        </MudButton>
                    </MudItem>
                    <MudItem xs="12" sm="6">
                        <MudButton Variant="Variant.Outlined" Color="Color.Warning" FullWidth="true" 
                                  StartIcon="@Icons.Material.Filled.Pets" Class="mb-2">
                            تقرير القطاع الحيواني
                        </MudButton>
                    </MudItem>
                    <MudItem xs="12" sm="6">
                        <MudButton Variant="Variant.Outlined" Color="Color.Info" FullWidth="true" 
                                  StartIcon="@Icons.Material.Filled.Water" Class="mb-2">
                            تقرير موارد المياه
                        </MudButton>
                    </MudItem>
                    <MudItem xs="12" sm="6">
                        <MudButton Variant="Variant.Outlined" Color="Color.Error" FullWidth="true" 
                                  StartIcon="@Icons.Material.Filled.FoodBank" Class="mb-2">
                            تقرير سلامة الغذاء
                        </MudButton>
                    </MudItem>
                    <MudItem xs="12">
                        <MudButton Variant="Variant.Filled" Color="Color.Primary" FullWidth="true" 
                                  StartIcon="@Icons.Material.Filled.Print" Href="/reports">
                            عرض جميع التقارير
                        </MudButton>
                    </MudItem>
                </MudGrid>
            </MudPaper>
        </MudItem>
    </MudGrid>
</MudContainer>

@code {
    // بيانات الرسم البياني
    private List<MonthlyData> _monthlyData = new();

    // الأنشطة الحديثة
    private List<RecentActivity> _recentActivities = new();

    protected override async Task OnInitializedAsync()
    {
        await LoadDashboardData();
    }

    private async Task LoadDashboardData()
    {
        // تحميل بيانات الرسم البياني
        _monthlyData = new List<MonthlyData>
        {
            new() { Month = "يناير", PlantSector = 1200, AnimalSector = 800, WaterSector = 400 },
            new() { Month = "فبراير", PlantSector = 1350, AnimalSector = 850, WaterSector = 420 },
            new() { Month = "مارس", PlantSector = 1500, AnimalSector = 900, WaterSector = 450 },
            new() { Month = "أبريل", PlantSector = 1650, AnimalSector = 950, WaterSector = 480 },
            new() { Month = "مايو", PlantSector = 1800, AnimalSector = 1000, WaterSector = 500 },
            new() { Month = "يونيو", PlantSector = 1680, AnimalSector = 980, WaterSector = 490 }
        };

        // تحميل الأنشطة الحديثة
        _recentActivities = new List<RecentActivity>
        {
            new() { Title = "تسجيل محصول جديد", Description = "تم تسجيل محصول طماطم في مزرعة الخير", Time = "منذ 5 دقائق", Color = Color.Success },
            new() { Title = "فحص جودة غذاء", Description = "تم إجراء فحص جودة لمنتجات الألبان", Time = "منذ 15 دقيقة", Color = Color.Error },
            new() { Title = "تحديث بيانات الإنتاج", Description = "تم تحديث بيانات إنتاج القطاع الحيواني", Time = "منذ 30 دقيقة", Color = Color.Warning },
            new() { Title = "تقرير مياه جديد", Description = "تم إنشاء تقرير استهلاك المياه الشهري", Time = "منذ ساعة", Color = Color.Info },
            new() { Title = "تفتيش رقابي", Description = "تم إجراء تفتيش رقابي على مزرعة النماء", Time = "منذ ساعتين", Color = Color.Dark }
        };

        await Task.Delay(100); // محاكاة التحميل
    }

    // النماذج
    public class MonthlyData
    {
        public string Month { get; set; } = string.Empty;
        public decimal PlantSector { get; set; }
        public decimal AnimalSector { get; set; }
        public decimal WaterSector { get; set; }
    }

    public class RecentActivity
    {
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Time { get; set; } = string.Empty;
        public Color Color { get; set; }
    }
}
