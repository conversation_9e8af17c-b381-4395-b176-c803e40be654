<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>نظام إحصائي - عرض تفاعلي</title>

    <!-- Google Fonts - Cairo for Arabic -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">

    <!-- Material Design Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">

    <!-- Bootstrap RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <style>
        * {
            font-family: 'Cairo', 'Roboto', sans-serif;
        }

        body {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            direction: rtl;
        }

        .sidebar {
            background: linear-gradient(180deg, rgba(33, 150, 243, 0.95) 0%, rgba(21, 101, 192, 0.95) 100%);
            backdrop-filter: blur(10px);
            min-height: 100vh;
            color: white;
            position: fixed;
            top: 0;
            right: 0;
            width: 280px;
            z-index: 1000;
            transition: transform 0.3s ease;
        }

        .sidebar.collapsed {
            transform: translateX(100%);
        }

        .main-content {
            margin-right: 280px;
            transition: margin-right 0.3s ease;
        }

        .main-content.expanded {
            margin-right: 0;
        }

        .nav-link {
            color: rgba(255, 255, 255, 0.8);
            border-radius: 8px;
            margin: 2px 0;
            transition: all 0.3s ease;
            padding: 12px 16px;
        }

        .nav-link:hover {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            transform: translateX(-2px);
        }

        .nav-link.active {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border-right: 3px solid white;
        }

        .card {
            border-radius: 16px;
            border: none;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            transition: transform 0.3s ease;
        }

        .card:hover {
            transform: translateY(-2px);
        }

        .stat-card {
            background: linear-gradient(135deg, #4caf50 0%, #66bb6a 100%);
            color: white;
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 40px rgba(76, 175, 80, 0.3);
        }

        .stat-card-blue {
            background: linear-gradient(135deg, #2196f3 0%, #42a5f5 100%);
        }

        .stat-card-blue:hover {
            box-shadow: 0 12px 40px rgba(33, 150, 243, 0.3);
        }

        .stat-card-orange {
            background: linear-gradient(135deg, #ff9800 0%, #ffb74d 100%);
        }

        .stat-card-orange:hover {
            box-shadow: 0 12px 40px rgba(255, 152, 0, 0.3);
        }

        .stat-card-purple {
            background: linear-gradient(135deg, #9c27b0 0%, #ba68c8 100%);
        }

        .stat-card-purple:hover {
            box-shadow: 0 12px 40px rgba(156, 39, 176, 0.3);
        }

        .page-header {
            background: linear-gradient(135deg, rgba(33, 150, 243, 0.1) 0%, rgba(21, 101, 192, 0.05) 100%);
            border-radius: 16px;
            border: 1px solid rgba(33, 150, 243, 0.1);
        }

        .btn-gradient {
            background: linear-gradient(45deg, #2196f3 0%, #1976d2 100%);
            border: none;
            border-radius: 25px;
            color: white;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(33, 150, 243, 0.3);
        }

        .btn-gradient:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(33, 150, 243, 0.4);
            color: white;
        }

        .material-icons {
            vertical-align: middle;
        }

        .table {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 12px;
        }

        .table th {
            background: linear-gradient(90deg, #2196f3 0%, #1976d2 100%);
            color: white;
            border: none;
        }

        .table td {
            border: none;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        .badge {
            border-radius: 20px;
            padding: 8px 12px;
        }

        .login-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .login-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
            max-width: 400px;
            width: 100%;
        }

        .fade-in {
            animation: fadeIn 0.6s ease-in-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .sector-tab.active {
            background: linear-gradient(135deg, #4caf50 0%, #66bb6a 100%);
            color: white;
            border-color: #4caf50;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(76, 175, 80, 0.3);
        }

        .sector-tab {
            display: flex !important;
            flex-direction: column !important;
            align-items: center !important;
            justify-content: center !important;
            padding: 15px 10px !important;
            text-align: center !important;
        }

        .sector-tab:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .sector-tab div {
            line-height: 1 !important;
            margin-bottom: 8px !important;
            display: block !important;
        }

        .sector-tab small {
            font-size: 0.75rem !important;
            text-align: center !important;
            line-height: 1.2 !important;
            display: block !important;
        }

        /* Enhanced emoji support */
        .sector-tab div {
            font-family: 'Segoe UI Emoji', 'Apple Color Emoji', 'Noto Color Emoji', 'Twemoji Mozilla', 'Android Emoji', sans-serif !important;
            text-rendering: optimizeLegibility !important;
            -webkit-font-smoothing: antialiased !important;
            -moz-osx-font-smoothing: grayscale !important;
        }

        /* Ensure buttons maintain proper layout */
        .sector-tab {
            border-radius: 12px !important;
            transition: all 0.3s ease !important;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
            position: relative !important;
        }

        .sector-tab:hover {
            transform: translateY(-3px) !important;
            box-shadow: 0 6px 12px rgba(40, 167, 69, 0.2) !important;
        }

        /* Ensure icons are always visible */
        .sector-tab .material-icons {
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
            font-family: 'Material Icons' !important;
            font-weight: normal !important;
            font-style: normal !important;
            line-height: 1 !important;
            letter-spacing: normal !important;
            text-transform: none !important;
            white-space: nowrap !important;
            word-wrap: normal !important;
            direction: ltr !important;
            -webkit-font-feature-settings: 'liga' !important;
            -webkit-font-smoothing: antialiased !important;
        }

        /* Force visibility for all sector tab content */
        .sector-tab * {
            visibility: visible !important;
            opacity: 1 !important;
        }

        /* Sub-section tabs styling */
        .palm-sub-tab, .fruit-sub-tab, .guidance-sub-tab {
            transition: all 0.3s ease;
            border-radius: 8px;
        }

        .palm-sub-tab:hover, .fruit-sub-tab:hover, .guidance-sub-tab:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .palm-sub-content, .fruit-sub-content, .guidance-sub-content {
            margin-top: 20px;
            padding: 20px;
            border-radius: 8px;
            background: #f8f9fa;
        }

        /* Wilayat filter buttons */
        .wilayat-filter {
            transition: all 0.3s ease;
        }

        .wilayat-filter:hover {
            transform: translateY(-1px);
        }

        .plant-section-content {
            animation: fadeIn 0.5s ease-in-out;
        }

        .mobile-toggle {
            display: none;
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1001;
            background: #2196f3;
            color: white;
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            box-shadow: 0 4px 12px rgba(33, 150, 243, 0.3);
        }

        @media (max-width: 768px) {
            .mobile-toggle {
                display: block;
            }

            .sidebar {
                transform: translateX(100%);
            }

            .sidebar.show {
                transform: translateX(0);
            }

            .main-content {
                margin-right: 0;
            }
        }

        .chart-container {
            position: relative;
            height: 300px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            padding: 20px;
        }
    </style>
</head>
<body>
    <!-- Login Screen -->
    <div id="loginScreen" class="login-container">
        <div class="login-card p-5">
            <div class="text-center mb-4">
                <span class="material-icons text-primary" style="font-size: 4rem;">analytics</span>
                <h3 class="text-primary fw-bold mt-3 mb-2">نظام إحصائي</h3>
                <p class="text-muted">وزارة الزراعة والثروة السمكية</p>
                <hr>
                <h5>تسجيل الدخول</h5>
            </div>

            <form id="loginForm">
                <div class="mb-3">
                    <label class="form-label">اسم المستخدم</label>
                    <input type="text" class="form-control" id="username" value="admin" required>
                </div>
                <div class="mb-3">
                    <label class="form-label">كلمة المرور</label>
                    <input type="password" class="form-control" id="password" value="admin" required>
                </div>
                <div class="mb-3 form-check">
                    <input type="checkbox" class="form-check-input" id="rememberMe">
                    <label class="form-check-label" for="rememberMe">تذكرني</label>
                </div>
                <button type="submit" class="btn btn-gradient w-100 mb-3">
                    <span class="material-icons me-2">login</span>
                    تسجيل الدخول
                </button>
            </form>

            <div class="alert alert-info">
                <strong>بيانات تجريبية:</strong><br>
                اسم المستخدم: <strong>admin</strong><br>
                كلمة المرور: <strong>admin</strong>
            </div>
        </div>
    </div>

    <!-- Main Application -->
    <div id="mainApp" style="display: none;">
        <!-- Mobile Toggle Button -->
        <button class="mobile-toggle" onclick="toggleSidebar()">
            <span class="material-icons">menu</span>
        </button>

        <!-- Sidebar -->
        <div class="sidebar" id="sidebar">
            <div class="p-3">
                <div class="text-center mb-4">
                    <span class="material-icons" style="font-size: 3rem;">analytics</span>
                    <h5 class="mt-2 mb-0">نظام إحصائي</h5>
                    <small class="opacity-75">وزارة الزراعة والثروة السمكية</small>
                </div>

                <nav class="nav flex-column">
                    <a class="nav-link active" href="#" onclick="showPage('dashboard')">
                        <span class="material-icons me-2">dashboard</span>
                        لوحة التحكم
                    </a>

                    <hr class="my-3 opacity-25">

                    <h6 class="text-white-50 px-3 mb-2">القطاعات</h6>

                    <a class="nav-link" href="#" onclick="showPage('plant-sector')">
                        <span class="material-icons me-2">eco</span>
                        القطاع النباتي
                    </a>

                    <a class="nav-link" href="#" onclick="showPage('animal-sector')">
                        <span class="material-icons me-2">pets</span>
                        القطاع الحيواني
                    </a>

                    <a class="nav-link" href="#" onclick="showPage('water-sector')">
                        <span class="material-icons me-2">water</span>
                        قطاع موارد المياه
                    </a>

                    <a class="nav-link" href="#" onclick="showPage('food-safety')">
                        <span class="material-icons me-2">food_bank</span>
                        جودة وسلامة الغذاء
                    </a>

                    <a class="nav-link" href="#" onclick="showPage('monitoring')">
                        <span class="material-icons me-2">security</span>
                        قطاع الرقابة
                    </a>

                    <a class="nav-link" href="#" onclick="showPage('admin-finance')">
                        <span class="material-icons me-2">business</span>
                        الشؤون الإدارية والمالية
                    </a>

                    <hr class="my-3 opacity-25">

                    <a class="nav-link" href="#" onclick="showPage('reports')">
                        <span class="material-icons me-2">assessment</span>
                        التقارير
                    </a>

                    <a class="nav-link" href="#" onclick="showPage('settings')">
                        <span class="material-icons me-2">settings</span>
                        الإعدادات
                    </a>

                    <hr class="my-3 opacity-25">

                    <a class="nav-link" href="#" onclick="logout()">
                        <span class="material-icons me-2">logout</span>
                        تسجيل الخروج
                    </a>
                </nav>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content" id="mainContent">
            <!-- Dashboard Page -->
            <div id="dashboard-page" class="page-content p-4">
                <!-- Welcome Header -->
                <div class="page-header p-4 mb-4 fade-in">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="text-primary fw-bold mb-1">مرحباً بك في نظام إحصائي</h2>
                            <p class="text-muted mb-0">وزارة الزراعة والثروة السمكية - سلطنة عمان</p>
                            <small class="text-muted" id="currentTime"></small>
                        </div>
                        <span class="material-icons text-primary" style="font-size: 3rem;">dashboard</span>
                    </div>
                </div>

                <!-- Statistics Cards -->
                <div class="row mb-4">
                    <div class="col-md-3 mb-3">
                        <div class="card stat-card p-3 fade-in" style="animation-delay: 0.1s;">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h3 class="fw-bold mb-0" id="totalSectors">6</h3>
                                    <small>القطاعات النشطة</small>
                                </div>
                                <span class="material-icons" style="font-size: 2.5rem;">category</span>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3 mb-3">
                        <div class="card stat-card stat-card-blue p-3 fade-in" style="animation-delay: 0.2s;">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h3 class="fw-bold mb-0" id="totalCrops">1,247</h3>
                                    <small>إجمالي المحاصيل</small>
                                </div>
                                <span class="material-icons" style="font-size: 2.5rem;">grass</span>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3 mb-3">
                        <div class="card stat-card stat-card-orange p-3 fade-in" style="animation-delay: 0.3s;">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h3 class="fw-bold mb-0" id="totalProduction">15,680</h3>
                                    <small>الإنتاج الإجمالي (طن)</small>
                                </div>
                                <span class="material-icons" style="font-size: 2.5rem;">agriculture</span>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3 mb-3">
                        <div class="card stat-card stat-card-purple p-3 fade-in" style="animation-delay: 0.4s;">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h3 class="fw-bold mb-0" id="totalFarms">156</h3>
                                    <small>المزارع النشطة</small>
                                </div>
                                <span class="material-icons" style="font-size: 2.5rem;">home</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Charts and Quick Access -->
                <div class="row">
                    <div class="col-md-8 mb-4">
                        <div class="card fade-in" style="animation-delay: 0.5s;">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0">
                                    <span class="material-icons me-2">trending_up</span>
                                    الإنتاج الشهري لجميع القطاعات
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="chart-container">
                                    <canvas id="productionChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4 mb-4">
                        <div class="card fade-in" style="animation-delay: 0.6s;">
                            <div class="card-header bg-success text-white">
                                <h5 class="mb-0">
                                    <span class="material-icons me-2">speed</span>
                                    الوصول السريع
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="d-grid gap-2">
                                    <button class="btn btn-outline-success" onclick="showPage('plant-sector')">
                                        <span class="material-icons me-2">eco</span>
                                        القطاع النباتي
                                    </button>
                                    <button class="btn btn-outline-warning" onclick="showPage('animal-sector')">
                                        <span class="material-icons me-2">pets</span>
                                        القطاع الحيواني
                                    </button>
                                    <button class="btn btn-outline-info" onclick="showPage('water-sector')">
                                        <span class="material-icons me-2">water</span>
                                        قطاع المياه
                                    </button>
                                    <button class="btn btn-outline-danger" onclick="showPage('food-safety')">
                                        <span class="material-icons me-2">food_bank</span>
                                        سلامة الغذاء
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Plant Sector Page -->
            <div id="plant-sector-page" class="page-content p-4" style="display: none;">
                <div class="page-header p-4 mb-4 fade-in" style="background: linear-gradient(135deg, rgba(76, 175, 80, 0.1) 0%, rgba(139, 195, 74, 0.1) 100%);">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="text-success fw-bold mb-1">القطاع النباتي</h2>
                            <p class="text-muted mb-0">إدارة ومراقبة الإنتاج الزراعي والمحاصيل النباتية</p>
                        </div>
                        <span class="material-icons text-success" style="font-size: 3rem;">eco</span>
                    </div>
                </div>

                <!-- Navigation Tabs -->
                <div class="card mb-4">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <span class="material-icons me-2">category</span>
                            أقسام القطاع النباتي
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-lg-3 col-md-4 col-sm-6 col-12 mb-3">
                                <button class="btn btn-outline-success w-100 sector-tab active" onclick="showPlantSection('palm-programs')" style="min-height: 120px; display: flex !important; flex-direction: column !important; align-items: center !important; justify-content: center !important; padding: 15px;">
                                    <div style="font-size: 3rem; margin-bottom: 10px; color: #28a745; font-weight: bold; font-family: 'Segoe UI Emoji', 'Apple Color Emoji', sans-serif;">🌴</div>
                                    <small style="text-align: center; font-size: 0.9rem; font-weight: 600;">برنامج نخيل التمر</small>
                                </button>
                            </div>
                            <div class="col-lg-3 col-md-4 col-sm-6 col-12 mb-3">
                                <button class="btn btn-outline-success w-100 sector-tab" onclick="showPlantSection('fruit-seedlings')" style="min-height: 120px; display: flex !important; flex-direction: column !important; align-items: center !important; justify-content: center !important; padding: 15px;">
                                    <div style="font-size: 3rem; margin-bottom: 10px; color: #28a745; font-weight: bold; font-family: 'Segoe UI Emoji', 'Apple Color Emoji', sans-serif;">🍎</div>
                                    <small style="text-align: center; font-size: 0.9rem; font-weight: 600;">برنامج شتلات الفاكهة المحسنة</small>
                                </button>
                            </div>
                            <div class="col-lg-3 col-md-4 col-sm-6 col-12 mb-3">
                                <button class="btn btn-outline-success w-100 sector-tab" onclick="showPlantSection('vegetable-crops')" style="min-height: 120px; display: flex !important; flex-direction: column !important; align-items: center !important; justify-content: center !important; padding: 15px;">
                                    <div style="font-size: 3rem; margin-bottom: 10px; color: #28a745; font-weight: bold; font-family: 'Segoe UI Emoji', 'Apple Color Emoji', sans-serif;">🥬</div>
                                    <small style="text-align: center; font-size: 0.9rem; font-weight: 600;">المحاصيل الخضرية</small>
                                </button>
                            </div>
                            <div class="col-lg-3 col-md-4 col-sm-6 col-12 mb-3">
                                <button class="btn btn-outline-warning w-100 sector-tab" onclick="showPlantSection('guidance-fields')" style="min-height: 120px; display: flex !important; flex-direction: column !important; align-items: center !important; justify-content: center !important; padding: 15px;">
                                    <div style="font-size: 3rem; margin-bottom: 10px; color: #ffc107; font-weight: bold; font-family: 'Segoe UI Emoji', 'Apple Color Emoji', sans-serif;">🌾</div>
                                    <small style="text-align: center; font-size: 0.9rem; font-weight: 600;">الحقول الإرشادية للحاصلات الحقلية</small>
                                </button>
                            </div>
                            <div class="col-lg-3 col-md-4 col-sm-6 col-12 mb-3">
                                <button class="btn btn-outline-info w-100 sector-tab" onclick="showPlantSection('field-crops')" style="min-height: 120px; display: flex !important; flex-direction: column !important; align-items: center !important; justify-content: center !important; padding: 15px;">
                                    <div style="font-size: 3rem; margin-bottom: 10px; color: #17a2b8; font-weight: bold; font-family: 'Segoe UI Emoji', 'Apple Color Emoji', sans-serif;">🚜</div>
                                    <small style="text-align: center; font-size: 0.9rem; font-weight: 600;">المحاصيل الحقلية العامة</small>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Section Content -->
                <div id="palm-programs" class="plant-section-content">
                    <div class="card">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0">
                                <span style="font-size: 1.5rem;">🌴</span>
                                برنامج نخيل التمر
                            </h5>
                        </div>
                        <div class="card-body">
                            <!-- أقسام فرعية لبرنامج نخيل التمر -->
                            <div class="row mb-4">
                                <div class="col-md-6 mb-3">
                                    <button class="btn btn-outline-primary w-100 palm-sub-tab active" onclick="showPalmSubSection('palm-seedlings')" style="min-height: 80px; display: flex; flex-direction: column; align-items: center; justify-content: center;">
                                        <div style="font-size: 2rem; margin-bottom: 5px; font-family: 'Segoe UI Emoji', 'Apple Color Emoji', sans-serif;">🌱</div>
                                        <span>فسائل النخيل</span>
                                    </button>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <button class="btn btn-outline-warning w-100 palm-sub-tab" onclick="showPalmSubSection('palm-renewal')" style="min-height: 80px; display: flex; flex-direction: column; align-items: center; justify-content: center;">
                                        <div style="font-size: 2rem; margin-bottom: 5px; font-family: 'Segoe UI Emoji', 'Apple Color Emoji', sans-serif;">🔄</div>
                                        <span>مشروع الإحلال والتجديد</span>
                                    </button>
                                </div>
                            </div>

                            <!-- محتوى فسائل النخيل -->
                            <div id="palm-seedlings-content" class="palm-sub-content">
                                <h6 class="text-primary mb-3">🌱 فسائل النخيل الموزعة</h6>

                                <div class="row mb-4">
                                    <div class="col-md-3">
                                        <div class="card stat-card text-white" style="background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);">
                                            <div class="card-body text-center">
                                                <h3>3,450</h3>
                                                <small>إجمالي الفسائل الموزعة</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="card stat-card text-white" style="background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);">
                                            <div class="card-body text-center">
                                                <h3>2,890</h3>
                                                <small>الفسائل المزروعة</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="card stat-card text-white" style="background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);">
                                            <div class="card-body text-center">
                                                <h3>83.8%</h3>
                                                <small>معدل النجاح</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="card stat-card text-white" style="background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);">
                                            <div class="card-body text-center">
                                                <h3>156</h3>
                                                <small>المستفيدين</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h6 class="text-primary mb-0">📊 سجل توزيع الفسائل</h6>
                                    <button class="btn btn-primary btn-sm" onclick="showAddSeedlingModal()">
                                        <span class="material-icons me-1">add</span>
                                        تسجيل توزيع فسائل
                                    </button>
                                </div>

                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead class="table-primary">
                                            <tr>
                                                <th>تاريخ التوزيع</th>
                                                <th>المستفيد</th>
                                                <th>المنطقة</th>
                                                <th>عدد الفسائل</th>
                                                <th>النوع</th>
                                                <th>حالة النمو</th>
                                                <th>ملاحظات</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody id="seedlings-table-body">
                                            <!-- سيتم ملء البيانات ديناميكياً -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <!-- محتوى مشروع الإحلال والتجديد -->
                            <div id="palm-renewal-content" class="palm-sub-content" style="display: none;">
                                <h6 class="text-warning mb-3">🔄 مشروع الإحلال والتجديد</h6>

                                <div class="row mb-4">
                                    <div class="col-md-3">
                                        <div class="card stat-card text-white" style="background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);">
                                            <div class="card-body text-center">
                                                <h3>450</h3>
                                                <small>الأشجار المستبدلة</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="card stat-card text-white" style="background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);">
                                            <div class="card-body text-center">
                                                <h3>320</h3>
                                                <small>الأشجار الجديدة</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="card stat-card text-white" style="background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);">
                                            <div class="card-body text-center">
                                                <h3>85%</h3>
                                                <small>معدل النجاح</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="card stat-card text-white" style="background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);">
                                            <div class="card-body text-center">
                                                <h3>25</h3>
                                                <small>المزارع المشاركة</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h6 class="text-warning mb-0">📊 سجل مشاريع الإحلال</h6>
                                    <button class="btn btn-warning btn-sm" onclick="showAddRenewalModal()">
                                        <span class="material-icons me-1">add</span>
                                        تسجيل مشروع إحلال
                                    </button>
                                </div>

                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead class="table-warning">
                                            <tr>
                                                <th>اسم المزرعة</th>
                                                <th>المنطقة</th>
                                                <th>عدد الأشجار المستبدلة</th>
                                                <th>عدد الأشجار الجديدة</th>
                                                <th>تاريخ البداية</th>
                                                <th>تاريخ الانتهاء</th>
                                                <th>الحالة</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>مزرعة التطوير</td>
                                                <td>الداخلية</td>
                                                <td>80</td>
                                                <td>75</td>
                                                <td>2024/01/15</td>
                                                <td>2024/06/15</td>
                                                <td><span class="badge bg-success">مكتمل</span></td>
                                                <td>
                                                    <button class="btn btn-sm btn-outline-info me-1" title="عرض">
                                                        <span class="material-icons">visibility</span>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-warning me-1" title="تعديل">
                                                        <span class="material-icons">edit</span>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-danger" title="حذف">
                                                        <span class="material-icons">delete</span>
                                                    </button>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>مزرعة النهضة</td>
                                                <td>الشرقية</td>
                                                <td>120</td>
                                                <td>110</td>
                                                <td>2024/03/01</td>
                                                <td>2024/08/01</td>
                                                <td><span class="badge bg-warning">قيد التنفيذ</span></td>
                                                <td>
                                                    <button class="btn btn-sm btn-outline-info me-1" title="عرض">
                                                        <span class="material-icons">visibility</span>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-warning me-1" title="تعديل">
                                                        <span class="material-icons">edit</span>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-danger" title="حذف">
                                                        <span class="material-icons">delete</span>
                                                    </button>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <div class="row mb-4">
                                <div class="col-md-3">
                                    <div class="card stat-card bg-success text-white">
                                        <div class="card-body text-center">
                                            <h3>1,245</h3>
                                            <small>إجمالي أشجار النخيل</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card stat-card bg-info text-white">
                                        <div class="card-body text-center">
                                            <h3>850</h3>
                                            <small>الأشجار المنتجة</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card stat-card bg-warning text-white">
                                        <div class="card-body text-center">
                                            <h3>2,340</h3>
                                            <small>الإنتاج (طن)</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card stat-card bg-danger text-white">
                                        <div class="card-body text-center">
                                            <h3>45</h3>
                                            <small>المزارع المسجلة</small>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h6 class="text-success mb-0">📊 بيانات مزارع النخيل</h6>
                                <button class="btn btn-success btn-sm" onclick="showAddPalmFarmModal()">
                                    <span class="material-icons me-1">add</span>
                                    إضافة مزرعة نخيل
                                </button>
                            </div>

                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead class="table-success">
                                        <tr>
                                            <th>اسم المزرعة</th>
                                            <th>المنطقة</th>
                                            <th>عدد الأشجار</th>
                                            <th>الأشجار المنتجة</th>
                                            <th>الإنتاج المتوقع (طن)</th>
                                            <th>نوع التمر</th>
                                            <th>الحالة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>مزرعة الواحة الخضراء</td>
                                            <td>الداخلية</td>
                                            <td>150</td>
                                            <td>120</td>
                                            <td>25.5</td>
                                            <td>خلاص</td>
                                            <td><span class="badge bg-success">ممتاز</span></td>
                                            <td>
                                                <button class="btn btn-sm btn-outline-info me-1" title="عرض">
                                                    <span class="material-icons">visibility</span>
                                                </button>
                                                <button class="btn btn-sm btn-outline-warning me-1" title="تعديل">
                                                    <span class="material-icons">edit</span>
                                                </button>
                                                <button class="btn btn-sm btn-outline-danger" title="حذف">
                                                    <span class="material-icons">delete</span>
                                                </button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>مزرعة النخيل الذهبي</td>
                                            <td>الشرقية</td>
                                            <td>200</td>
                                            <td>180</td>
                                            <td>42.0</td>
                                            <td>فرض</td>
                                            <td><span class="badge bg-success">ممتاز</span></td>
                                            <td>
                                                <button class="btn btn-sm btn-outline-info me-1" title="عرض">
                                                    <span class="material-icons">visibility</span>
                                                </button>
                                                <button class="btn btn-sm btn-outline-warning me-1" title="تعديل">
                                                    <span class="material-icons">edit</span>
                                                </button>
                                                <button class="btn btn-sm btn-outline-danger" title="حذف">
                                                    <span class="material-icons">delete</span>
                                                </button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>مزرعة التمور الملكية</td>
                                            <td>الظاهرة</td>
                                            <td>95</td>
                                            <td>75</td>
                                            <td>18.2</td>
                                            <td>خصاب</td>
                                            <td><span class="badge bg-warning">جيد</span></td>
                                            <td>
                                                <button class="btn btn-sm btn-outline-info me-1" title="عرض">
                                                    <span class="material-icons">visibility</span>
                                                </button>
                                                <button class="btn btn-sm btn-outline-warning me-1" title="تعديل">
                                                    <span class="material-icons">edit</span>
                                                </button>
                                                <button class="btn btn-sm btn-outline-danger" title="حذف">
                                                    <span class="material-icons">delete</span>
                                                </button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>مزرعة الأصيل</td>
                                            <td>البريمي</td>
                                            <td>300</td>
                                            <td>250</td>
                                            <td>65.8</td>
                                            <td>مبروم</td>
                                            <td><span class="badge bg-success">ممتاز</span></td>
                                            <td>
                                                <button class="btn btn-sm btn-outline-info me-1" title="عرض">
                                                    <span class="material-icons">visibility</span>
                                                </button>
                                                <button class="btn btn-sm btn-outline-warning me-1" title="تعديل">
                                                    <span class="material-icons">edit</span>
                                                </button>
                                                <button class="btn btn-sm btn-outline-danger" title="حذف">
                                                    <span class="material-icons">delete</span>
                                                </button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="fruit-seedlings" class="plant-section-content" style="display: none;">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">
                                <span style="font-size: 1.5rem;">🍎</span>
                                برنامج شتلات الفاكهة المحسنة
                            </h5>
                        </div>
                        <div class="card-body">
                            <!-- أقسام فرعية لبرنامج شتلات الفاكهة -->
                            <div class="row mb-4">
                                <div class="col-md-6 mb-3">
                                    <button class="btn btn-outline-success w-100 fruit-sub-tab active" onclick="showFruitSubSection('fruit-fields')" style="min-height: 80px; display: flex; flex-direction: column; align-items: center; justify-content: center;">
                                        <div style="font-size: 2rem; margin-bottom: 5px; font-family: 'Segoe UI Emoji', 'Apple Color Emoji', sans-serif;">🌳</div>
                                        <span>إنشاء حقول الفاكهة</span>
                                    </button>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <button class="btn btn-outline-info w-100 fruit-sub-tab" onclick="showFruitSubSection('fruit-distribution')" style="min-height: 80px; display: flex; flex-direction: column; align-items: center; justify-content: center;">
                                        <div style="font-size: 2rem; margin-bottom: 5px; font-family: 'Segoe UI Emoji', 'Apple Color Emoji', sans-serif;">🚚</div>
                                        <span>توزيع شتلات الفاكهة</span>
                                    </button>
                                </div>
                            </div>

                            <!-- محتوى إنشاء حقول الفاكهة -->
                            <div id="fruit-fields-content" class="fruit-sub-content">
                                <h6 class="text-success mb-3">🌳 حقول الفاكهة المنشأة</h6>

                                <div class="row mb-4">
                                    <div class="col-md-3">
                                        <div class="card stat-card text-white" style="background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);">
                                            <div class="card-body text-center">
                                                <h3>8,450</h3>
                                                <small>إجمالي أشجار الفاكهة</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="card stat-card text-white" style="background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);">
                                            <div class="card-body text-center">
                                                <h3>6,200</h3>
                                                <small>الأشجار المنتجة</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="card stat-card text-white" style="background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);">
                                            <div class="card-body text-center">
                                                <h3>1,850</h3>
                                                <small>الإنتاج (طن)</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="card stat-card text-white" style="background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);">
                                            <div class="card-body text-center">
                                                <h3>15</h3>
                                                <small>أنواع الفاكهة</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h6 class="text-success mb-0">📊 بيانات حقول الفاكهة</h6>
                                    <button class="btn btn-success btn-sm" onclick="showAddFruitFieldModal()">
                                        <span class="material-icons me-1">add</span>
                                        إضافة حقل فاكهة
                                    </button>
                                </div>

                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead class="table-success">
                                            <tr>
                                                <th>نوع الفاكهة</th>
                                                <th>المنطقة</th>
                                                <th>عدد الأشجار</th>
                                                <th>الأشجار المنتجة</th>
                                                <th>الإنتاج (طن)</th>
                                                <th>موسم الحصاد</th>
                                                <th>الحالة الصحية</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>المانجو</td>
                                                <td>ظفار</td>
                                                <td>1,200</td>
                                                <td>950</td>
                                                <td>285</td>
                                                <td>يونيو - أغسطس</td>
                                                <td><span class="badge bg-success">ممتاز</span></td>
                                                <td>
                                                    <button class="btn btn-sm btn-outline-info me-1" title="عرض">
                                                        <span class="material-icons">visibility</span>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-warning me-1" title="تعديل">
                                                        <span class="material-icons">edit</span>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-danger" title="حذف">
                                                        <span class="material-icons">delete</span>
                                                    </button>
                                                </td>
                                            </tr>
                                            <!-- المزيد من البيانات -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <!-- محتوى توزيع شتلات الفاكهة -->
                            <div id="fruit-distribution-content" class="fruit-sub-content" style="display: none;">
                                <h6 class="text-info mb-3">🚚 توزيع شتلات الفاكهة</h6>
                                <!-- محتوى توزيع الشتلات -->
                            </div>
                        </div>
                    </div>
                </div>

                <div id="palm-seedlings" class="plant-section-content" style="display: none;">
                    <div class="card">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0">
                                <span style="font-size: 1.5rem;">🌱</span>
                                فسائل النخيل الموزعة
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-4">
                                <div class="col-md-4">
                                    <div class="card stat-card bg-primary text-white">
                                        <div class="card-body text-center">
                                            <h3>3,450</h3>
                                            <small>إجمالي الفسائل الموزعة</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="card stat-card bg-success text-white">
                                        <div class="card-body text-center">
                                            <h3>2,890</h3>
                                            <small>الفسائل المزروعة</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="card stat-card bg-info text-white">
                                        <div class="card-body text-center">
                                            <h3>83.8%</h3>
                                            <small>معدل النجاح</small>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h6 class="text-success mb-0">📊 سجل توزيع الفسائل</h6>
                                <button class="btn btn-success btn-sm" onclick="showAddSeedlingModal()">
                                    <span class="material-icons me-1">add</span>
                                    تسجيل توزيع فسائل
                                </button>
                            </div>

                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead class="table-success">
                                        <tr>
                                            <th>تاريخ التوزيع</th>
                                            <th>المستفيد</th>
                                            <th>المنطقة</th>
                                            <th>عدد الفسائل</th>
                                            <th>النوع</th>
                                            <th>حالة النمو</th>
                                            <th>ملاحظات</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>2024/06/15</td>
                                            <td>أحمد بن سالم</td>
                                            <td>الداخلية</td>
                                            <td>25</td>
                                            <td>خلاص</td>
                                            <td><span class="badge bg-success">ممتاز</span></td>
                                            <td>نمو طبيعي</td>
                                            <td>
                                                <button class="btn btn-sm btn-outline-info me-1" title="عرض">
                                                    <span class="material-icons">visibility</span>
                                                </button>
                                                <button class="btn btn-sm btn-outline-warning me-1" title="تعديل">
                                                    <span class="material-icons">edit</span>
                                                </button>
                                                <button class="btn btn-sm btn-outline-danger" title="حذف">
                                                    <span class="material-icons">delete</span>
                                                </button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>2024/06/10</td>
                                            <td>فاطمة بنت محمد</td>
                                            <td>الشرقية</td>
                                            <td>15</td>
                                            <td>فرض</td>
                                            <td><span class="badge bg-warning">جيد</span></td>
                                            <td>يحتاج متابعة</td>
                                            <td>
                                                <button class="btn btn-sm btn-outline-info me-1" title="عرض">
                                                    <span class="material-icons">visibility</span>
                                                </button>
                                                <button class="btn btn-sm btn-outline-warning me-1" title="تعديل">
                                                    <span class="material-icons">edit</span>
                                                </button>
                                                <button class="btn btn-sm btn-outline-danger" title="حذف">
                                                    <span class="material-icons">delete</span>
                                                </button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>2024/06/05</td>
                                            <td>سعيد بن علي</td>
                                            <td>الظاهرة</td>
                                            <td>30</td>
                                            <td>خصاب</td>
                                            <td><span class="badge bg-success">ممتاز</span></td>
                                            <td>نمو سريع</td>
                                            <td>
                                                <button class="btn btn-sm btn-outline-info me-1" title="عرض">
                                                    <span class="material-icons">visibility</span>
                                                </button>
                                                <button class="btn btn-sm btn-outline-warning me-1" title="تعديل">
                                                    <span class="material-icons">edit</span>
                                                </button>
                                                <button class="btn btn-sm btn-outline-danger" title="حذف">
                                                    <span class="material-icons">delete</span>
                                                </button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="guidance-fields" class="plant-section-content" style="display: none;">
                    <div class="card">
                        <div class="card-header bg-warning text-dark">
                            <h5 class="mb-0">
                                <span style="font-size: 1.5rem;">🌾</span>
                                الحقول الإرشادية للحاصلات الحقلية - محافظة الداخلية
                            </h5>
                        </div>
                        <div class="card-body">
                            <!-- أقسام فرعية للحقول الإرشادية -->
                            <div class="row mb-4">
                                <div class="col-md-4 mb-3">
                                    <button class="btn btn-outline-success w-100 guidance-sub-tab active" onclick="showGuidanceSubSection('wheat-crop')" style="min-height: 80px; display: flex; flex-direction: column; align-items: center; justify-content: center;">
                                        <div style="font-size: 2rem; margin-bottom: 5px; font-family: 'Segoe UI Emoji', 'Apple Color Emoji', sans-serif;">🌾</div>
                                        <span>محصول القمح</span>
                                    </button>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <button class="btn btn-outline-primary w-100 guidance-sub-tab" onclick="showGuidanceSubSection('participating-farmers')" style="min-height: 80px; display: flex; flex-direction: column; align-items: center; justify-content: center;">
                                        <div style="font-size: 2rem; margin-bottom: 5px; font-family: 'Segoe UI Emoji', 'Apple Color Emoji', sans-serif;">👥</div>
                                        <span>المزارعين المشتركين داخل البرنامج</span>
                                    </button>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <button class="btn btn-outline-info w-100 guidance-sub-tab" onclick="showGuidanceSubSection('all-farmers')" style="min-height: 80px; display: flex; flex-direction: column; align-items: center; justify-content: center;">
                                        <div style="font-size: 2rem; margin-bottom: 5px; font-family: 'Segoe UI Emoji', 'Apple Color Emoji', sans-serif;">🧑‍🌾</div>
                                        <span>المزارعين</span>
                                    </button>
                                </div>
                            </div>

                            <!-- محتوى محصول القمح -->
                            <div id="wheat-crop-content" class="guidance-sub-content">
                                <h6 class="text-success mb-3">🌾 محصول القمح - الحقول الإرشادية</h6>

                                <div class="row mb-4">
                                    <div class="col-md-3">
                                        <div class="card stat-card text-white" style="background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);">
                                            <div class="card-body text-center">
                                                <h3>25</h3>
                                                <small>الحقول الإرشادية</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="card stat-card text-white" style="background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);">
                                            <div class="card-body text-center">
                                                <h3>450</h3>
                                                <small>المساحة (هكتار)</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="card stat-card text-white" style="background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);">
                                            <div class="card-body text-center">
                                                <h3>1,350</h3>
                                                <small>الإنتاج المتوقع (طن)</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="card stat-card text-white" style="background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);">
                                            <div class="card-body text-center">
                                                <h3>8</h3>
                                                <small>الولايات المشاركة</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h6 class="text-success mb-0">📊 بيانات الحقول الإرشادية للقمح</h6>
                                    <button class="btn btn-success btn-sm" onclick="showAddWheatFieldModal()">
                                        <span class="material-icons me-1">add</span>
                                        إضافة حقل إرشادي
                                    </button>
                                </div>

                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead class="table-success">
                                            <tr>
                                                <th>اسم الحقل</th>
                                                <th>الولاية</th>
                                                <th>المزارع المسؤول</th>
                                                <th>المساحة (هكتار)</th>
                                                <th>الصنف المزروع</th>
                                                <th>تاريخ الزراعة</th>
                                                <th>الحالة</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>حقل نزوى الإرشادي</td>
                                                <td>نزوى</td>
                                                <td>سالم بن أحمد الكندي</td>
                                                <td>25</td>
                                                <td>قمح محسن</td>
                                                <td>2024/11/15</td>
                                                <td><span class="badge bg-success">نمو ممتاز</span></td>
                                                <td>
                                                    <button class="btn btn-sm btn-outline-info me-1" title="عرض">
                                                        <span class="material-icons">visibility</span>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-warning me-1" title="تعديل">
                                                        <span class="material-icons">edit</span>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-danger" title="حذف">
                                                        <span class="material-icons">delete</span>
                                                    </button>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>حقل سمائل التجريبي</td>
                                                <td>سمائل</td>
                                                <td>محمد بن سعيد الهنائي</td>
                                                <td>18</td>
                                                <td>قمح مقاوم للجفاف</td>
                                                <td>2024/11/20</td>
                                                <td><span class="badge bg-info">إنبات</span></td>
                                                <td>
                                                    <button class="btn btn-sm btn-outline-info me-1" title="عرض">
                                                        <span class="material-icons">visibility</span>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-warning me-1" title="تعديل">
                                                        <span class="material-icons">edit</span>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-danger" title="حذف">
                                                        <span class="material-icons">delete</span>
                                                    </button>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>حقل بهلاء النموذجي</td>
                                                <td>بهلاء</td>
                                                <td>علي بن خالد البهلاني</td>
                                                <td>30</td>
                                                <td>قمح عالي الإنتاج</td>
                                                <td>2024/11/10</td>
                                                <td><span class="badge bg-warning">يحتاج ري</span></td>
                                                <td>
                                                    <button class="btn btn-sm btn-outline-info me-1" title="عرض">
                                                        <span class="material-icons">visibility</span>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-warning me-1" title="تعديل">
                                                        <span class="material-icons">edit</span>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-danger" title="حذف">
                                                        <span class="material-icons">delete</span>
                                                    </button>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <!-- محتوى المزارعين المشتركين داخل البرنامج -->
                            <div id="participating-farmers-content" class="guidance-sub-content" style="display: none;">
                                <h6 class="text-primary mb-3">👥 المزارعين المشتركين داخل البرنامج</h6>

                                <div class="row mb-4">
                                    <div class="col-md-3">
                                        <div class="card stat-card text-white" style="background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);">
                                            <div class="card-body text-center">
                                                <h3>45</h3>
                                                <small>المزارعين المشتركين</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="card stat-card text-white" style="background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);">
                                            <div class="card-body text-center">
                                                <h3>8</h3>
                                                <small>الولايات المشاركة</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="card stat-card text-white" style="background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);">
                                            <div class="card-body text-center">
                                                <h3>380</h3>
                                                <small>المساحة الإجمالية (هكتار)</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="card stat-card text-white" style="background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);">
                                            <div class="card-body text-center">
                                                <h3>95%</h3>
                                                <small>معدل المشاركة النشطة</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h6 class="text-primary mb-0">📊 سجل المزارعين المشتركين</h6>
                                    <button class="btn btn-primary btn-sm" onclick="showAddParticipatingFarmerModal()">
                                        <span class="material-icons me-1">add</span>
                                        إضافة مزارع مشترك
                                    </button>
                                </div>

                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead class="table-primary">
                                            <tr>
                                                <th>اسم المزارع</th>
                                                <th>الولاية</th>
                                                <th>رقم الهاتف</th>
                                                <th>المساحة المشاركة (هكتار)</th>
                                                <th>نوع المشاركة</th>
                                                <th>تاريخ الانضمام</th>
                                                <th>الحالة</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>سالم بن أحمد الكندي</td>
                                                <td>نزوى</td>
                                                <td>99123456</td>
                                                <td>25</td>
                                                <td>حقل إرشادي</td>
                                                <td>2024/01/15</td>
                                                <td><span class="badge bg-success">نشط</span></td>
                                                <td>
                                                    <button class="btn btn-sm btn-outline-info me-1" title="عرض">
                                                        <span class="material-icons">visibility</span>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-warning me-1" title="تعديل">
                                                        <span class="material-icons">edit</span>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-danger" title="حذف">
                                                        <span class="material-icons">delete</span>
                                                    </button>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>محمد بن سعيد الهنائي</td>
                                                <td>سمائل</td>
                                                <td>99234567</td>
                                                <td>18</td>
                                                <td>حقل تجريبي</td>
                                                <td>2024/02/01</td>
                                                <td><span class="badge bg-success">نشط</span></td>
                                                <td>
                                                    <button class="btn btn-sm btn-outline-info me-1" title="عرض">
                                                        <span class="material-icons">visibility</span>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-warning me-1" title="تعديل">
                                                        <span class="material-icons">edit</span>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-danger" title="حذف">
                                                        <span class="material-icons">delete</span>
                                                    </button>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>علي بن خالد البهلاني</td>
                                                <td>بهلاء</td>
                                                <td>99345678</td>
                                                <td>30</td>
                                                <td>حقل نموذجي</td>
                                                <td>2024/01/20</td>
                                                <td><span class="badge bg-warning">متابعة</span></td>
                                                <td>
                                                    <button class="btn btn-sm btn-outline-info me-1" title="عرض">
                                                        <span class="material-icons">visibility</span>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-warning me-1" title="تعديل">
                                                        <span class="material-icons">edit</span>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-danger" title="حذف">
                                                        <span class="material-icons">delete</span>
                                                    </button>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <!-- محتوى المزارعين -->
                            <div id="all-farmers-content" class="guidance-sub-content" style="display: none;">
                                <h6 class="text-info mb-3">🧑‍🌾 جميع المزارعين - محافظة الداخلية</h6>

                                <div class="row mb-4">
                                    <div class="col-md-3">
                                        <div class="card stat-card text-white" style="background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);">
                                            <div class="card-body text-center">
                                                <h3>1,250</h3>
                                                <small>إجمالي المزارعين</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="card stat-card text-white" style="background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);">
                                            <div class="card-body text-center">
                                                <h3>8</h3>
                                                <small>الولايات</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="card stat-card text-white" style="background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);">
                                            <div class="card-body text-center">
                                                <h3>2,850</h3>
                                                <small>المساحة الإجمالية (هكتار)</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="card stat-card text-white" style="background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);">
                                            <div class="card-body text-center">
                                                <h3>85%</h3>
                                                <small>المزارعين النشطين</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- فلتر حسب الولايات -->
                                <div class="row mb-4">
                                    <div class="col-md-12">
                                        <h6 class="text-info mb-3">🏛️ فلتر حسب الولايات</h6>
                                        <div class="row">
                                            <div class="col-md-3 mb-2">
                                                <button class="btn btn-outline-info btn-sm w-100 wilayat-filter active" onclick="filterByWilayat('all')">جميع الولايات</button>
                                            </div>
                                            <div class="col-md-3 mb-2">
                                                <button class="btn btn-outline-secondary btn-sm w-100 wilayat-filter" onclick="filterByWilayat('نزوى')">نزوى</button>
                                            </div>
                                            <div class="col-md-3 mb-2">
                                                <button class="btn btn-outline-secondary btn-sm w-100 wilayat-filter" onclick="filterByWilayat('سمائل')">سمائل</button>
                                            </div>
                                            <div class="col-md-3 mb-2">
                                                <button class="btn btn-outline-secondary btn-sm w-100 wilayat-filter" onclick="filterByWilayat('بهلاء')">بهلاء</button>
                                            </div>
                                            <div class="col-md-3 mb-2">
                                                <button class="btn btn-outline-secondary btn-sm w-100 wilayat-filter" onclick="filterByWilayat('أدم')">أدم</button>
                                            </div>
                                            <div class="col-md-3 mb-2">
                                                <button class="btn btn-outline-secondary btn-sm w-100 wilayat-filter" onclick="filterByWilayat('الحمراء')">الحمراء</button>
                                            </div>
                                            <div class="col-md-3 mb-2">
                                                <button class="btn btn-outline-secondary btn-sm w-100 wilayat-filter" onclick="filterByWilayat('منح')">منح</button>
                                            </div>
                                            <div class="col-md-3 mb-2">
                                                <button class="btn btn-outline-secondary btn-sm w-100 wilayat-filter" onclick="filterByWilayat('إزكي')">إزكي</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h6 class="text-info mb-0">📊 سجل جميع المزارعين</h6>
                                    <button class="btn btn-info btn-sm" onclick="showAddFarmerModal()">
                                        <span class="material-icons me-1">add</span>
                                        إضافة مزارع جديد
                                    </button>
                                </div>

                                <div class="table-responsive">
                                    <table class="table table-striped" id="farmers-table">
                                        <thead class="table-info">
                                            <tr>
                                                <th>اسم المزارع</th>
                                                <th>الولاية</th>
                                                <th>رقم الهاتف</th>
                                                <th>المساحة (هكتار)</th>
                                                <th>نوع المحصول</th>
                                                <th>تاريخ التسجيل</th>
                                                <th>الحالة</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr data-wilayat="نزوى">
                                                <td>سالم بن أحمد الكندي</td>
                                                <td>نزوى</td>
                                                <td>99123456</td>
                                                <td>25</td>
                                                <td>قمح</td>
                                                <td>2024/01/15</td>
                                                <td><span class="badge bg-success">نشط</span></td>
                                                <td>
                                                    <button class="btn btn-sm btn-outline-info me-1" title="عرض">
                                                        <span class="material-icons">visibility</span>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-warning me-1" title="تعديل">
                                                        <span class="material-icons">edit</span>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-danger" title="حذف">
                                                        <span class="material-icons">delete</span>
                                                    </button>
                                                </td>
                                            </tr>
                                            <tr data-wilayat="سمائل">
                                                <td>محمد بن سعيد الهنائي</td>
                                                <td>سمائل</td>
                                                <td>99234567</td>
                                                <td>18</td>
                                                <td>شعير</td>
                                                <td>2024/02/01</td>
                                                <td><span class="badge bg-success">نشط</span></td>
                                                <td>
                                                    <button class="btn btn-sm btn-outline-info me-1" title="عرض">
                                                        <span class="material-icons">visibility</span>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-warning me-1" title="تعديل">
                                                        <span class="material-icons">edit</span>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-danger" title="حذف">
                                                        <span class="material-icons">delete</span>
                                                    </button>
                                                </td>
                                            </tr>
                                            <tr data-wilayat="بهلاء">
                                                <td>علي بن خالد البهلاني</td>
                                                <td>بهلاء</td>
                                                <td>99345678</td>
                                                <td>30</td>
                                                <td>ذرة</td>
                                                <td>2024/01/20</td>
                                                <td><span class="badge bg-warning">متابعة</span></td>
                                                <td>
                                                    <button class="btn btn-sm btn-outline-info me-1" title="عرض">
                                                        <span class="material-icons">visibility</span>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-warning me-1" title="تعديل">
                                                        <span class="material-icons">edit</span>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-danger" title="حذف">
                                                        <span class="material-icons">delete</span>
                                                    </button>
                                                </td>
                                            </tr>
                                            <tr data-wilayat="أدم">
                                                <td>أحمد بن محمد الأدمي</td>
                                                <td>أدم</td>
                                                <td>99456789</td>
                                                <td>22</td>
                                                <td>قمح</td>
                                                <td>2024/03/10</td>
                                                <td><span class="badge bg-success">نشط</span></td>
                                                <td>
                                                    <button class="btn btn-sm btn-outline-info me-1" title="عرض">
                                                        <span class="material-icons">visibility</span>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-warning me-1" title="تعديل">
                                                        <span class="material-icons">edit</span>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-danger" title="حذف">
                                                        <span class="material-icons">delete</span>
                                                    </button>
                                                </td>
                                            </tr>
                                            <tr data-wilayat="الحمراء">
                                                <td>خالد بن سالم الحمراوي</td>
                                                <td>الحمراء</td>
                                                <td>99567890</td>
                                                <td>15</td>
                                                <td>برسيم</td>
                                                <td>2024/02/15</td>
                                                <td><span class="badge bg-info">جديد</span></td>
                                                <td>
                                                    <button class="btn btn-sm btn-outline-info me-1" title="عرض">
                                                        <span class="material-icons">visibility</span>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-warning me-1" title="تعديل">
                                                        <span class="material-icons">edit</span>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-danger" title="حذف">
                                                        <span class="material-icons">delete</span>
                                                    </button>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="vegetable-crops" class="plant-section-content" style="display: none;">
                    <div class="card">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0">
                                <span style="font-size: 1.5rem;">🥬</span>
                                المحاصيل الخضرية
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped" id="cropsTable">
                                    <thead class="table-success">
                                        <tr>
                                            <th>اسم المحصول</th>
                                            <th>النوع</th>
                                            <th>المساحة (هكتار)</th>
                                            <th>الإنتاج المتوقع (طن)</th>
                                            <th>تاريخ الزراعة</th>
                                            <th>الحالة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody id="cropsTableBody">
                                        <!-- سيتم ملؤها بالجافا سكريبت -->
                                    </tbody>
                                </table>
                            </div>
                            <button class="btn btn-success mt-3" onclick="showAddCropModal()">
                                <span class="material-icons me-1">add</span>
                                إضافة محصول جديد
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Placeholder sections for other categories -->
                <div id="field-crops" class="plant-section-content" style="display: none;">
                    <div class="card">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0">
                                <span style="font-size: 1.5rem;">🌾</span>
                                المحاصيل الحقلية
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-4">
                                <div class="col-md-3">
                                    <div class="card stat-card bg-warning text-white">
                                        <div class="card-body text-center">
                                            <h3>1,850</h3>
                                            <small>المساحة المزروعة (هكتار)</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card stat-card bg-info text-white">
                                        <div class="card-body text-center">
                                            <h3>4,200</h3>
                                            <small>الإنتاج المتوقع (طن)</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card stat-card bg-success text-white">
                                        <div class="card-body text-center">
                                            <h3>12</h3>
                                            <small>أنواع المحاصيل</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card stat-card bg-danger text-white">
                                        <div class="card-body text-center">
                                            <h3>67</h3>
                                            <small>المزارع المسجلة</small>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h6 class="text-warning mb-0">📊 بيانات المحاصيل الحقلية</h6>
                                <button class="btn btn-warning btn-sm" onclick="showAddFieldCropModal()">
                                    <span class="material-icons me-1">add</span>
                                    إضافة محصول حقلي
                                </button>
                            </div>

                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead class="table-warning">
                                        <tr>
                                            <th>نوع المحصول</th>
                                            <th>المنطقة</th>
                                            <th>المساحة (هكتار)</th>
                                            <th>الإنتاج المتوقع (طن)</th>
                                            <th>موسم الزراعة</th>
                                            <th>طريقة الري</th>
                                            <th>الحالة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>القمح</td>
                                            <td>الداخلية</td>
                                            <td>450</td>
                                            <td>1,200</td>
                                            <td>شتوي</td>
                                            <td>بالرش</td>
                                            <td><span class="badge bg-success">نمو جيد</span></td>
                                        </tr>
                                        <tr>
                                            <td>الشعير</td>
                                            <td>الظاهرة</td>
                                            <td>320</td>
                                            <td>850</td>
                                            <td>شتوي</td>
                                            <td>بالتنقيط</td>
                                            <td><span class="badge bg-warning">يحتاج متابعة</span></td>
                                        </tr>
                                        <tr>
                                            <td>الذرة الصفراء</td>
                                            <td>الشرقية</td>
                                            <td>280</td>
                                            <td>980</td>
                                            <td>صيفي</td>
                                            <td>بالرش</td>
                                            <td><span class="badge bg-success">ممتاز</span></td>
                                        </tr>
                                        <tr>
                                            <td>الذرة البيضاء</td>
                                            <td>البريمي</td>
                                            <td>200</td>
                                            <td>650</td>
                                            <td>صيفي</td>
                                            <td>بالغمر</td>
                                            <td><span class="badge bg-info">إزهار</span></td>
                                        </tr>
                                        <tr>
                                            <td>الأرز</td>
                                            <td>الباطنة</td>
                                            <td>150</td>
                                            <td>750</td>
                                            <td>صيفي</td>
                                            <td>بالغمر</td>
                                            <td><span class="badge bg-success">نضج</span></td>
                                        </tr>
                                        <tr>
                                            <td>البرسيم الحجازي</td>
                                            <td>الداخلية</td>
                                            <td>350</td>
                                            <td>2,100</td>
                                            <td>دائم</td>
                                            <td>بالتنقيط</td>
                                            <td><span class="badge bg-success">إنتاج مستمر</span></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="fruit-trees" class="plant-section-content" style="display: none;">
                    <div class="card">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0">
                                <span style="font-size: 1.5rem;">🍎</span>
                                أشجار الفاكهة
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-4">
                                <div class="col-md-3">
                                    <div class="card stat-card bg-primary text-white">
                                        <div class="card-body text-center">
                                            <h3>8,450</h3>
                                            <small>إجمالي الأشجار</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card stat-card bg-success text-white">
                                        <div class="card-body text-center">
                                            <h3>6,200</h3>
                                            <small>الأشجار المنتجة</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card stat-card bg-warning text-white">
                                        <div class="card-body text-center">
                                            <h3>1,850</h3>
                                            <small>الإنتاج (طن)</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card stat-card bg-info text-white">
                                        <div class="card-body text-center">
                                            <h3>15</h3>
                                            <small>أنواع الفواكه</small>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h6 class="text-primary mb-0">📊 بيانات أشجار الفاكهة</h6>
                                <button class="btn btn-primary btn-sm" onclick="showAddFruitTreeModal()">
                                    <span class="material-icons me-1">add</span>
                                    إضافة بيانات أشجار فاكهة
                                </button>
                            </div>

                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead class="table-primary">
                                        <tr>
                                            <th>نوع الفاكهة</th>
                                            <th>المنطقة</th>
                                            <th>عدد الأشجار</th>
                                            <th>الأشجار المنتجة</th>
                                            <th>الإنتاج (طن)</th>
                                            <th>موسم الحصاد</th>
                                            <th>الحالة الصحية</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>المانجو</td>
                                            <td>الباطنة</td>
                                            <td>1,200</td>
                                            <td>950</td>
                                            <td>285</td>
                                            <td>مايو - يوليو</td>
                                            <td><span class="badge bg-success">ممتاز</span></td>
                                        </tr>
                                        <tr>
                                            <td>الليمون</td>
                                            <td>الداخلية</td>
                                            <td>800</td>
                                            <td>650</td>
                                            <td>195</td>
                                            <td>نوفمبر - فبراير</td>
                                            <td><span class="badge bg-success">جيد جداً</span></td>
                                        </tr>
                                        <tr>
                                            <td>البرتقال</td>
                                            <td>الشرقية</td>
                                            <td>950</td>
                                            <td>780</td>
                                            <td>234</td>
                                            <td>ديسمبر - مارس</td>
                                            <td><span class="badge bg-warning">يحتاج رعاية</span></td>
                                        </tr>
                                        <tr>
                                            <td>الرمان</td>
                                            <td>الظاهرة</td>
                                            <td>650</td>
                                            <td>520</td>
                                            <td>156</td>
                                            <td>سبتمبر - نوفمبر</td>
                                            <td><span class="badge bg-success">ممتاز</span></td>
                                        </tr>
                                        <tr>
                                            <td>التين</td>
                                            <td>مسندم</td>
                                            <td>450</td>
                                            <td>380</td>
                                            <td>95</td>
                                            <td>يونيو - أغسطس</td>
                                            <td><span class="badge bg-info">جيد</span></td>
                                        </tr>
                                        <tr>
                                            <td>الجوافة</td>
                                            <td>الباطنة</td>
                                            <td>720</td>
                                            <td>600</td>
                                            <td>180</td>
                                            <td>أكتوبر - يناير</td>
                                            <td><span class="badge bg-success">ممتاز</span></td>
                                        </tr>
                                        <tr>
                                            <td>العنب</td>
                                            <td>الداخلية</td>
                                            <td>580</td>
                                            <td>480</td>
                                            <td>144</td>
                                            <td>يوليو - سبتمبر</td>
                                            <td><span class="badge bg-warning">متوسط</span></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="ornamental-plants" class="plant-section-content" style="display: none;">
                    <div class="card">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0">
                                <span style="font-size: 1.5rem;">🌺</span>
                                النباتات الزينة والتجميل
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-4">
                                <div class="col-md-4">
                                    <div class="card stat-card bg-pink text-white" style="background: linear-gradient(135deg, #e91e63 0%, #f06292 100%);">
                                        <div class="card-body text-center">
                                            <h3>25,600</h3>
                                            <small>النباتات المزروعة</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="card stat-card bg-purple text-white" style="background: linear-gradient(135deg, #9c27b0 0%, #ba68c8 100%);">
                                        <div class="card-body text-center">
                                            <h3>180</h3>
                                            <small>الحدائق العامة</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="card stat-card bg-teal text-white" style="background: linear-gradient(135deg, #009688 0%, #4db6ac 100%);">
                                        <div class="card-body text-center">
                                            <h3>45</h3>
                                            <small>أنواع النباتات</small>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h6 class="mb-0" style="color: #e91e63;">📊 بيانات النباتات الزينة</h6>
                                <button class="btn btn-sm" style="background: linear-gradient(135deg, #e91e63 0%, #f06292 100%); color: white;" onclick="showAddOrnamentalModal()">
                                    <span class="material-icons me-1">add</span>
                                    إضافة نباتات زينة
                                </button>
                            </div>

                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead style="background: linear-gradient(135deg, #e91e63 0%, #f06292 100%); color: white;">
                                        <tr>
                                            <th>نوع النبات</th>
                                            <th>الموقع</th>
                                            <th>العدد المزروع</th>
                                            <th>تاريخ الزراعة</th>
                                            <th>نوع التربة</th>
                                            <th>طريقة الري</th>
                                            <th>حالة النمو</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>الورد الجوري</td>
                                            <td>حديقة الأزهار - مسقط</td>
                                            <td>1,500</td>
                                            <td>2024/03/15</td>
                                            <td>طينية مخصبة</td>
                                            <td>بالتنقيط</td>
                                            <td><span class="badge bg-success">ازدهار</span></td>
                                        </tr>
                                        <tr>
                                            <td>النخيل الزينة</td>
                                            <td>كورنيش مطرح</td>
                                            <td>350</td>
                                            <td>2024/02/20</td>
                                            <td>رملية</td>
                                            <td>بالرش</td>
                                            <td><span class="badge bg-success">ممتاز</span></td>
                                        </tr>
                                        <tr>
                                            <td>الياسمين</td>
                                            <td>حديقة القرم</td>
                                            <td>800</td>
                                            <td>2024/04/10</td>
                                            <td>مختلطة</td>
                                            <td>بالتنقيط</td>
                                            <td><span class="badge bg-info">نمو جيد</span></td>
                                        </tr>
                                        <tr>
                                            <td>البوغانفيليا</td>
                                            <td>شارع السلطان قابوس</td>
                                            <td>1,200</td>
                                            <td>2024/01/25</td>
                                            <td>رملية طينية</td>
                                            <td>بالرش</td>
                                            <td><span class="badge bg-warning">يحتاج تقليم</span></td>
                                        </tr>
                                        <tr>
                                            <td>الفيكس</td>
                                            <td>مجمع السيف - صلالة</td>
                                            <td>450</td>
                                            <td>2024/03/05</td>
                                            <td>طينية</td>
                                            <td>بالتنقيط</td>
                                            <td><span class="badge bg-success">كثيف</span></td>
                                        </tr>
                                        <tr>
                                            <td>الكركديه</td>
                                            <td>حديقة الصحوة</td>
                                            <td>600</td>
                                            <td>2024/04/01</td>
                                            <td>مخصبة</td>
                                            <td>بالتنقيط</td>
                                            <td><span class="badge bg-success">إزهار مستمر</span></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="medicinal-plants" class="plant-section-content" style="display: none;">
                    <div class="card">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0">
                                <span style="font-size: 1.5rem;">🌿</span>
                                النباتات الطبية والعطرية
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-4">
                                <div class="col-md-3">
                                    <div class="card stat-card text-white" style="background: linear-gradient(135deg, #4caf50 0%, #66bb6a 100%);">
                                        <div class="card-body text-center">
                                            <h3>2,850</h3>
                                            <small>النباتات المزروعة</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card stat-card text-white" style="background: linear-gradient(135deg, #8bc34a 0%, #aed581 100%);">
                                        <div class="card-body text-center">
                                            <h3>18</h3>
                                            <small>الأنواع المزروعة</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card stat-card text-white" style="background: linear-gradient(135deg, #cddc39 0%, #dce775 100%);">
                                        <div class="card-body text-center">
                                            <h3>450</h3>
                                            <small>الإنتاج (كجم)</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card stat-card text-white" style="background: linear-gradient(135deg, #689f38 0%, #8bc34a 100%);">
                                        <div class="card-body text-center">
                                            <h3>12</h3>
                                            <small>المزارع المتخصصة</small>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h6 class="text-success mb-0">📊 بيانات النباتات الطبية</h6>
                                <button class="btn btn-success btn-sm" onclick="showAddMedicinalModal()">
                                    <span class="material-icons me-1">add</span>
                                    إضافة نباتات طبية
                                </button>
                            </div>

                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead style="background: linear-gradient(135deg, #4caf50 0%, #66bb6a 100%); color: white;">
                                        <tr>
                                            <th>النبات الطبي</th>
                                            <th>الاسم العلمي</th>
                                            <th>المنطقة</th>
                                            <th>الكمية المزروعة</th>
                                            <th>الاستخدام الطبي</th>
                                            <th>موسم الحصاد</th>
                                            <th>الحالة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>الألوة فيرا</td>
                                            <td>Aloe vera</td>
                                            <td>الداخلية</td>
                                            <td>450 نبتة</td>
                                            <td>علاج الحروق والجروح</td>
                                            <td>على مدار السنة</td>
                                            <td><span class="badge bg-success">ممتاز</span></td>
                                        </tr>
                                        <tr>
                                            <td>الريحان</td>
                                            <td>Ocimum basilicum</td>
                                            <td>الباطنة</td>
                                            <td>320 نبتة</td>
                                            <td>مضاد للالتهابات</td>
                                            <td>مايو - سبتمبر</td>
                                            <td><span class="badge bg-success">نمو جيد</span></td>
                                        </tr>
                                        <tr>
                                            <td>النعناع</td>
                                            <td>Mentha piperita</td>
                                            <td>الشرقية</td>
                                            <td>280 نبتة</td>
                                            <td>مهدئ للجهاز الهضمي</td>
                                            <td>أبريل - أكتوبر</td>
                                            <td><span class="badge bg-info">إزهار</span></td>
                                        </tr>
                                        <tr>
                                            <td>اللافندر</td>
                                            <td>Lavandula angustifolia</td>
                                            <td>الظاهرة</td>
                                            <td>200 نبتة</td>
                                            <td>مهدئ ومضاد للقلق</td>
                                            <td>يونيو - أغسطس</td>
                                            <td><span class="badge bg-warning">يحتاج ري</span></td>
                                        </tr>
                                        <tr>
                                            <td>الزعتر</td>
                                            <td>Thymus vulgaris</td>
                                            <td>مسندم</td>
                                            <td>350 نبتة</td>
                                            <td>مضاد للبكتيريا</td>
                                            <td>مارس - نوفمبر</td>
                                            <td><span class="badge bg-success">حصاد جاهز</span></td>
                                        </tr>
                                        <tr>
                                            <td>الكركم</td>
                                            <td>Curcuma longa</td>
                                            <td>ظفار</td>
                                            <td>180 نبتة</td>
                                            <td>مضاد للالتهابات</td>
                                            <td>ديسمبر - فبراير</td>
                                            <td><span class="badge bg-info">نمو الجذور</span></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="greenhouses" class="plant-section-content" style="display: none;">
                    <div class="card">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0">
                                <span style="font-size: 1.5rem;">🏠</span>
                                البيوت المحمية والزراعة المحكومة
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-4">
                                <div class="col-md-3">
                                    <div class="card stat-card text-white" style="background: linear-gradient(135deg, #607d8b 0%, #78909c 100%);">
                                        <div class="card-body text-center">
                                            <h3>145</h3>
                                            <small>إجمالي البيوت المحمية</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card stat-card text-white" style="background: linear-gradient(135deg, #795548 0%, #8d6e63 100%);">
                                        <div class="card-body text-center">
                                            <h3>28,500</h3>
                                            <small>المساحة (م²)</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card stat-card text-white" style="background: linear-gradient(135deg, #4caf50 0%, #66bb6a 100%);">
                                        <div class="card-body text-center">
                                            <h3>2,850</h3>
                                            <small>الإنتاج (طن)</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card stat-card text-white" style="background: linear-gradient(135deg, #ff9800 0%, #ffb74d 100%);">
                                        <div class="card-body text-center">
                                            <h3>89</h3>
                                            <small>المزارع المالكة</small>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h6 class="mb-0" style="color: #607d8b;">📊 بيانات البيوت المحمية</h6>
                                <button class="btn btn-sm text-white" style="background: linear-gradient(135deg, #607d8b 0%, #78909c 100%);" onclick="showAddGreenhouseModal()">
                                    <span class="material-icons me-1">add</span>
                                    إضافة بيت محمي
                                </button>
                            </div>

                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead style="background: linear-gradient(135deg, #607d8b 0%, #78909c 100%); color: white;">
                                        <tr>
                                            <th>اسم المزرعة</th>
                                            <th>المنطقة</th>
                                            <th>عدد البيوت</th>
                                            <th>المساحة (م²)</th>
                                            <th>نوع المحصول</th>
                                            <th>نظام التحكم</th>
                                            <th>الإنتاجية</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>مزرعة التقنيات الحديثة</td>
                                            <td>الباطنة</td>
                                            <td>25</td>
                                            <td>5,000</td>
                                            <td>طماطم هجين</td>
                                            <td>تحكم مناخي كامل</td>
                                            <td><span class="badge bg-success">عالية</span></td>
                                        </tr>
                                        <tr>
                                            <td>مزرعة الخضار المتطورة</td>
                                            <td>الداخلية</td>
                                            <td>18</td>
                                            <td>3,600</td>
                                            <td>خيار وفلفل</td>
                                            <td>هيدروبونيك</td>
                                            <td><span class="badge bg-success">ممتازة</span></td>
                                        </tr>
                                        <tr>
                                            <td>مزرعة الورقيات</td>
                                            <td>الشرقية</td>
                                            <td>12</td>
                                            <td>2,400</td>
                                            <td>خس وسبانخ</td>
                                            <td>NFT System</td>
                                            <td><span class="badge bg-info">جيدة</span></td>
                                        </tr>
                                        <tr>
                                            <td>مزرعة الفراولة</td>
                                            <td>الظاهرة</td>
                                            <td>8</td>
                                            <td>1,600</td>
                                            <td>فراولة</td>
                                            <td>تربة معدلة</td>
                                            <td><span class="badge bg-warning">متوسطة</span></td>
                                        </tr>
                                        <tr>
                                            <td>مزرعة الأعشاب</td>
                                            <td>مسقط</td>
                                            <td>15</td>
                                            <td>3,000</td>
                                            <td>ريحان وبقدونس</td>
                                            <td>ري بالتنقيط</td>
                                            <td><span class="badge bg-success">عالية</span></td>
                                        </tr>
                                        <tr>
                                            <td>مزرعة الزهور</td>
                                            <td>ظفار</td>
                                            <td>20</td>
                                            <td>4,000</td>
                                            <td>ورد وقرنفل</td>
                                            <td>تحكم حراري</td>
                                            <td><span class="badge bg-info">جيدة جداً</span></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="irrigation" class="plant-section-content" style="display: none;">
                    <div class="card">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0">
                                <span style="font-size: 1.5rem;">💧</span>
                                أنظمة الري والمياه الزراعية
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-4">
                                <div class="col-md-3">
                                    <div class="card stat-card text-white" style="background: linear-gradient(135deg, #2196f3 0%, #42a5f5 100%);">
                                        <div class="card-body text-center">
                                            <h3>1,250</h3>
                                            <small>أنظمة الري المركبة</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card stat-card text-white" style="background: linear-gradient(135deg, #00bcd4 0%, #4dd0e1 100%);">
                                        <div class="card-body text-center">
                                            <h3>85,600</h3>
                                            <small>استهلاك المياه (م³/شهر)</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card stat-card text-white" style="background: linear-gradient(135deg, #009688 0%, #4db6ac 100%);">
                                        <div class="card-body text-center">
                                            <h3>78%</h3>
                                            <small>كفاءة استخدام المياه</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card stat-card text-white" style="background: linear-gradient(135deg, #03a9f4 0%, #29b6f6 100%);">
                                        <div class="card-body text-center">
                                            <h3>156</h3>
                                            <small>المزارع المخدومة</small>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h6 class="text-primary mb-0">📊 بيانات أنظمة الري</h6>
                                <button class="btn btn-primary btn-sm" onclick="showAddIrrigationModal()">
                                    <span class="material-icons me-1">add</span>
                                    إضافة نظام ري
                                </button>
                            </div>

                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead style="background: linear-gradient(135deg, #2196f3 0%, #42a5f5 100%); color: white;">
                                        <tr>
                                            <th>نوع نظام الري</th>
                                            <th>المنطقة</th>
                                            <th>عدد الأنظمة</th>
                                            <th>المساحة المخدومة (هكتار)</th>
                                            <th>استهلاك المياه (م³/يوم)</th>
                                            <th>الكفاءة (%)</th>
                                            <th>الحالة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>الري بالتنقيط</td>
                                            <td>الداخلية</td>
                                            <td>450</td>
                                            <td>1,200</td>
                                            <td>2,400</td>
                                            <td>85%</td>
                                            <td><span class="badge bg-success">ممتاز</span></td>
                                        </tr>
                                        <tr>
                                            <td>الري بالرش</td>
                                            <td>الباطنة</td>
                                            <td>320</td>
                                            <td>850</td>
                                            <td>1,700</td>
                                            <td>75%</td>
                                            <td><span class="badge bg-info">جيد</span></td>
                                        </tr>
                                        <tr>
                                            <td>الري المحوري</td>
                                            <td>الشرقية</td>
                                            <td>85</td>
                                            <td>680</td>
                                            <td>1,360</td>
                                            <td>70%</td>
                                            <td><span class="badge bg-warning">يحتاج صيانة</span></td>
                                        </tr>
                                        <tr>
                                            <td>الري بالغمر</td>
                                            <td>الظاهرة</td>
                                            <td>180</td>
                                            <td>450</td>
                                            <td>1,800</td>
                                            <td>45%</td>
                                            <td><span class="badge bg-danger">منخفض الكفاءة</span></td>
                                        </tr>
                                        <tr>
                                            <td>الهيدروبونيك</td>
                                            <td>مسقط</td>
                                            <td>45</td>
                                            <td>25</td>
                                            <td>150</td>
                                            <td>95%</td>
                                            <td><span class="badge bg-success">عالي الكفاءة</span></td>
                                        </tr>
                                        <tr>
                                            <td>الري الذكي</td>
                                            <td>ظفار</td>
                                            <td>120</td>
                                            <td>380</td>
                                            <td>760</td>
                                            <td>88%</td>
                                            <td><span class="badge bg-success">تقنية متقدمة</span></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="fertilizers" class="plant-section-content" style="display: none;">
                    <div class="card">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0">
                                <span style="font-size: 1.5rem;">⚗️</span>
                                الأسمدة والمبيدات الزراعية
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-4">
                                <div class="col-md-3">
                                    <div class="card stat-card text-white" style="background: linear-gradient(135deg, #ff5722 0%, #ff7043 100%);">
                                        <div class="card-body text-center">
                                            <h3>2,450</h3>
                                            <small>كمية الأسمدة (طن)</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card stat-card text-white" style="background: linear-gradient(135deg, #e91e63 0%, #f06292 100%);">
                                        <div class="card-body text-center">
                                            <h3>1,850</h3>
                                            <small>كمية المبيدات (لتر)</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card stat-card text-white" style="background: linear-gradient(135deg, #9c27b0 0%, #ba68c8 100%);">
                                        <div class="card-body text-center">
                                            <h3>145</h3>
                                            <small>المزارع المستفيدة</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card stat-card text-white" style="background: linear-gradient(135deg, #673ab7 0%, #9575cd 100%);">
                                        <div class="card-body text-center">
                                            <h3>28</h3>
                                            <small>أنواع المنتجات</small>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="d-flex justify-content-between align-items-center mb-4">
                                <h6 class="mb-0" style="color: #ff5722;">📊 بيانات الأسمدة والمبيدات</h6>
                                <button class="btn btn-sm text-white" style="background: linear-gradient(135deg, #ff5722 0%, #ff7043 100%);" onclick="showAddFertilizerModal()">
                                    <span class="material-icons me-1">add</span>
                                    إضافة أسمدة/مبيدات
                                </button>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <h6 class="text-success mb-3">🌱 الأسمدة المستخدمة</h6>
                                    <div class="table-responsive">
                                        <table class="table table-sm table-striped">
                                            <thead style="background: linear-gradient(135deg, #4caf50 0%, #66bb6a 100%); color: white;">
                                                <tr>
                                                    <th>نوع السماد</th>
                                                    <th>الكمية (طن)</th>
                                                    <th>المحصول المستهدف</th>
                                                    <th>الحالة</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td>NPK 20-20-20</td>
                                                    <td>450</td>
                                                    <td>الخضروات</td>
                                                    <td><span class="badge bg-success">متوفر</span></td>
                                                </tr>
                                                <tr>
                                                    <td>يوريا 46%</td>
                                                    <td>380</td>
                                                    <td>المحاصيل الحقلية</td>
                                                    <td><span class="badge bg-warning">منخفض</span></td>
                                                </tr>
                                                <tr>
                                                    <td>سوبر فوسفات</td>
                                                    <td>320</td>
                                                    <td>أشجار الفاكهة</td>
                                                    <td><span class="badge bg-success">متوفر</span></td>
                                                </tr>
                                                <tr>
                                                    <td>كبريتات البوتاسيوم</td>
                                                    <td>280</td>
                                                    <td>النخيل</td>
                                                    <td><span class="badge bg-info">جيد</span></td>
                                                </tr>
                                                <tr>
                                                    <td>السماد العضوي</td>
                                                    <td>650</td>
                                                    <td>جميع المحاصيل</td>
                                                    <td><span class="badge bg-success">وفير</span></td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <h6 class="text-danger mb-3">🐛 المبيدات المستخدمة</h6>
                                    <div class="table-responsive">
                                        <table class="table table-sm table-striped">
                                            <thead style="background: linear-gradient(135deg, #f44336 0%, #ef5350 100%); color: white;">
                                                <tr>
                                                    <th>نوع المبيد</th>
                                                    <th>الكمية (لتر)</th>
                                                    <th>الآفة المستهدفة</th>
                                                    <th>الحالة</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td>مبيد حشري عضوي</td>
                                                    <td>320</td>
                                                    <td>المن والذباب الأبيض</td>
                                                    <td><span class="badge bg-success">آمن</span></td>
                                                </tr>
                                                <tr>
                                                    <td>مبيد فطري</td>
                                                    <td>280</td>
                                                    <td>البياض الدقيقي</td>
                                                    <td><span class="badge bg-warning">محدود الاستخدام</span></td>
                                                </tr>
                                                <tr>
                                                    <td>مبيد أعشاب</td>
                                                    <td>450</td>
                                                    <td>الحشائش العريضة</td>
                                                    <td><span class="badge bg-info">مراقب</span></td>
                                                </tr>
                                                <tr>
                                                    <td>مبيد نيماتودا</td>
                                                    <td>180</td>
                                                    <td>نيماتودا الجذور</td>
                                                    <td><span class="badge bg-danger">عالي السمية</span></td>
                                                </tr>
                                                <tr>
                                                    <td>مبيد بيولوجي</td>
                                                    <td>220</td>
                                                    <td>دودة ورق القطن</td>
                                                    <td><span class="badge bg-success">صديق للبيئة</span></td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="diseases" class="plant-section-content" style="display: none;">
                    <div class="card">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0">
                                <span style="font-size: 1.5rem;">🐛</span>
                                الآفات والأمراض النباتية
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-4">
                                <div class="col-md-3">
                                    <div class="card stat-card text-white" style="background: linear-gradient(135deg, #f44336 0%, #ef5350 100%);">
                                        <div class="card-body text-center">
                                            <h3>45</h3>
                                            <small>الحالات المسجلة</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card stat-card text-white" style="background: linear-gradient(135deg, #ff9800 0%, #ffb74d 100%);">
                                        <div class="card-body text-center">
                                            <h3>38</h3>
                                            <small>الحالات المعالجة</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card stat-card text-white" style="background: linear-gradient(135deg, #4caf50 0%, #66bb6a 100%);">
                                        <div class="card-body text-center">
                                            <h3>84%</h3>
                                            <small>معدل الشفاء</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card stat-card text-white" style="background: linear-gradient(135deg, #2196f3 0%, #42a5f5 100%);">
                                        <div class="card-body text-center">
                                            <h3>12</h3>
                                            <small>أنواع الآفات</small>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h6 class="text-danger mb-0">📊 سجل الآفات والأمراض</h6>
                                <button class="btn btn-danger btn-sm" onclick="showAddDiseaseModal()">
                                    <span class="material-icons me-1">add</span>
                                    تسجيل آفة/مرض
                                </button>
                            </div>

                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead style="background: linear-gradient(135deg, #f44336 0%, #ef5350 100%); color: white;">
                                        <tr>
                                            <th>نوع الآفة/المرض</th>
                                            <th>المحصول المصاب</th>
                                            <th>المنطقة</th>
                                            <th>تاريخ الاكتشاف</th>
                                            <th>شدة الإصابة</th>
                                            <th>العلاج المطبق</th>
                                            <th>الحالة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>المن الأخضر</td>
                                            <td>الطماطم</td>
                                            <td>الباطنة</td>
                                            <td>2024/06/10</td>
                                            <td><span class="badge bg-warning">متوسطة</span></td>
                                            <td>مبيد حشري عضوي</td>
                                            <td><span class="badge bg-success">تحت السيطرة</span></td>
                                        </tr>
                                        <tr>
                                            <td>البياض الدقيقي</td>
                                            <td>الخيار</td>
                                            <td>الداخلية</td>
                                            <td>2024/06/08</td>
                                            <td><span class="badge bg-danger">شديدة</span></td>
                                            <td>مبيد فطري</td>
                                            <td><span class="badge bg-warning">قيد العلاج</span></td>
                                        </tr>
                                        <tr>
                                            <td>دودة ورق القطن</td>
                                            <td>الباذنجان</td>
                                            <td>الشرقية</td>
                                            <td>2024/06/05</td>
                                            <td><span class="badge bg-warning">متوسطة</span></td>
                                            <td>مبيد بيولوجي</td>
                                            <td><span class="badge bg-success">شفاء تام</span></td>
                                        </tr>
                                        <tr>
                                            <td>الذبابة البيضاء</td>
                                            <td>الفلفل</td>
                                            <td>الظاهرة</td>
                                            <td>2024/06/12</td>
                                            <td><span class="badge bg-info">خفيفة</span></td>
                                            <td>مصائد لاصقة</td>
                                            <td><span class="badge bg-success">تحت السيطرة</span></td>
                                        </tr>
                                        <tr>
                                            <td>نيماتودا الجذور</td>
                                            <td>الجزر</td>
                                            <td>مسندم</td>
                                            <td>2024/06/01</td>
                                            <td><span class="badge bg-danger">شديدة</span></td>
                                            <td>تعقيم التربة</td>
                                            <td><span class="badge bg-warning">قيد المتابعة</span></td>
                                        </tr>
                                        <tr>
                                            <td>صدأ الأوراق</td>
                                            <td>القمح</td>
                                            <td>البريمي</td>
                                            <td>2024/05/28</td>
                                            <td><span class="badge bg-warning">متوسطة</span></td>
                                            <td>مبيد فطري وقائي</td>
                                            <td><span class="badge bg-success">شفاء تام</span></td>
                                        </tr>
                                        <tr>
                                            <td>حفار ساق النخيل</td>
                                            <td>النخيل</td>
                                            <td>ظفار</td>
                                            <td>2024/05/25</td>
                                            <td><span class="badge bg-danger">شديدة</span></td>
                                            <td>حقن كيميائي</td>
                                            <td><span class="badge bg-info">تحسن ملحوظ</span></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="research" class="plant-section-content" style="display: none;">
                    <div class="card">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0">
                                <span style="font-size: 1.5rem;">🔬</span>
                                البحوث والتطوير الزراعي
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-4">
                                <div class="col-md-3">
                                    <div class="card stat-card text-white" style="background: linear-gradient(135deg, #3f51b5 0%, #5c6bc0 100%);">
                                        <div class="card-body text-center">
                                            <h3>28</h3>
                                            <small>المشاريع البحثية النشطة</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card stat-card text-white" style="background: linear-gradient(135deg, #9c27b0 0%, #ba68c8 100%);">
                                        <div class="card-body text-center">
                                            <h3>15</h3>
                                            <small>المشاريع المكتملة</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card stat-card text-white" style="background: linear-gradient(135deg, #ff9800 0%, #ffb74d 100%);">
                                        <div class="card-body text-center">
                                            <h3>45</h3>
                                            <small>الباحثين المشاركين</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card stat-card text-white" style="background: linear-gradient(135deg, #4caf50 0%, #66bb6a 100%);">
                                        <div class="card-body text-center">
                                            <h3>12</h3>
                                            <small>الأصناف المطورة</small>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h6 class="mb-0" style="color: #3f51b5;">📊 المشاريع البحثية</h6>
                                <button class="btn btn-sm text-white" style="background: linear-gradient(135deg, #3f51b5 0%, #5c6bc0 100%);" onclick="showAddResearchModal()">
                                    <span class="material-icons me-1">add</span>
                                    إضافة مشروع بحثي
                                </button>
                            </div>

                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead style="background: linear-gradient(135deg, #3f51b5 0%, #5c6bc0 100%); color: white;">
                                        <tr>
                                            <th>اسم المشروع البحثي</th>
                                            <th>الباحث الرئيسي</th>
                                            <th>مجال البحث</th>
                                            <th>تاريخ البداية</th>
                                            <th>المدة المتوقعة</th>
                                            <th>نسبة الإنجاز</th>
                                            <th>الحالة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>تطوير أصناف طماطم مقاومة للملوحة</td>
                                            <td>د. أحمد الفارسي</td>
                                            <td>تربية النباتات</td>
                                            <td>2024/01/15</td>
                                            <td>18 شهر</td>
                                            <td>65%</td>
                                            <td><span class="badge bg-info">قيد التنفيذ</span></td>
                                        </tr>
                                        <tr>
                                            <td>استخدام التقنيات الحيوية في إكثار النخيل</td>
                                            <td>د. فاطمة الشامسي</td>
                                            <td>زراعة الأنسجة</td>
                                            <td>2023/09/01</td>
                                            <td>24 شهر</td>
                                            <td>85%</td>
                                            <td><span class="badge bg-warning">مرحلة التقييم</span></td>
                                        </tr>
                                        <tr>
                                            <td>تحسين كفاءة استخدام المياه في الزراعة</td>
                                            <td>د. سالم البلوشي</td>
                                            <td>إدارة المياه</td>
                                            <td>2024/03/10</td>
                                            <td>12 شهر</td>
                                            <td>40%</td>
                                            <td><span class="badge bg-info">قيد التنفيذ</span></td>
                                        </tr>
                                        <tr>
                                            <td>المكافحة البيولوجية لآفات الخضروات</td>
                                            <td>د. مريم الكندي</td>
                                            <td>وقاية النباتات</td>
                                            <td>2023/11/20</td>
                                            <td>15 شهر</td>
                                            <td>90%</td>
                                            <td><span class="badge bg-success">مكتمل</span></td>
                                        </tr>
                                        <tr>
                                            <td>تطوير أسمدة عضوية محلية</td>
                                            <td>د. خالد الحراصي</td>
                                            <td>خصوبة التربة</td>
                                            <td>2024/02/05</td>
                                            <td>20 شهر</td>
                                            <td>30%</td>
                                            <td><span class="badge bg-info">مرحلة مبكرة</span></td>
                                        </tr>
                                        <tr>
                                            <td>دراسة تأثير التغير المناخي على المحاصيل</td>
                                            <td>د. عائشة الرواحي</td>
                                            <td>المناخ الزراعي</td>
                                            <td>2023/08/15</td>
                                            <td>30 شهر</td>
                                            <td>70%</td>
                                            <td><span class="badge bg-info">قيد التنفيذ</span></td>
                                        </tr>
                                        <tr>
                                            <td>تطوير تقنيات الزراعة الذكية</td>
                                            <td>د. محمد الغافري</td>
                                            <td>التكنولوجيا الزراعية</td>
                                            <td>2024/04/01</td>
                                            <td>36 شهر</td>
                                            <td>15%</td>
                                            <td><span class="badge bg-primary">بداية المشروع</span></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Other Sector Pages (Placeholder) -->
            <div id="animal-sector-page" class="page-content p-4" style="display: none;">
                <div class="page-header p-4 mb-4" style="background: linear-gradient(135deg, rgba(255, 152, 0, 0.1) 0%, rgba(255, 183, 77, 0.1) 100%);">
                    <h2 class="text-warning fw-bold">القطاع الحيواني</h2>
                    <p class="text-muted">إدارة الثروة الحيوانية والإنتاج الحيواني</p>
                </div>
                <div class="alert alert-info">
                    <h5>قريباً...</h5>
                    <p>سيتم تطوير هذا القطاع في المرحلة التالية من المشروع.</p>
                </div>
            </div>

            <div id="water-sector-page" class="page-content p-4" style="display: none;">
                <div class="page-header p-4 mb-4" style="background: linear-gradient(135deg, rgba(33, 150, 243, 0.1) 0%, rgba(66, 165, 245, 0.1) 100%);">
                    <h2 class="text-info fw-bold">قطاع موارد المياه</h2>
                    <p class="text-muted">إدارة ومراقبة موارد المياه واستهلاكها</p>
                </div>
                <div class="alert alert-info">
                    <h5>قريباً...</h5>
                    <p>سيتم تطوير هذا القطاع في المرحلة التالية من المشروع.</p>
                </div>
            </div>

            <div id="food-safety-page" class="page-content p-4" style="display: none;">
                <div class="page-header p-4 mb-4" style="background: linear-gradient(135deg, rgba(244, 67, 54, 0.1) 0%, rgba(239, 83, 80, 0.1) 100%);">
                    <h2 class="text-danger fw-bold">قطاع جودة وسلامة الغذاء</h2>
                    <p class="text-muted">مراقبة جودة وسلامة المنتجات الغذائية</p>
                </div>
                <div class="alert alert-info">
                    <h5>قريباً...</h5>
                    <p>سيتم تطوير هذا القطاع في المرحلة التالية من المشروع.</p>
                </div>
            </div>

            <div id="monitoring-page" class="page-content p-4" style="display: none;">
                <div class="page-header p-4 mb-4" style="background: linear-gradient(135deg, rgba(66, 66, 66, 0.1) 0%, rgba(97, 97, 97, 0.1) 100%);">
                    <h2 class="text-dark fw-bold">قطاع الرقابة</h2>
                    <p class="text-muted">التفتيش والمراقبة والإجراءات التصحيحية</p>
                </div>
                <div class="alert alert-info">
                    <h5>قريباً...</h5>
                    <p>سيتم تطوير هذا القطاع في المرحلة التالية من المشروع.</p>
                </div>
            </div>

            <div id="admin-finance-page" class="page-content p-4" style="display: none;">
                <div class="page-header p-4 mb-4" style="background: linear-gradient(135deg, rgba(156, 39, 176, 0.1) 0%, rgba(186, 104, 200, 0.1) 100%);">
                    <h2 class="text-secondary fw-bold">قطاع الشؤون الإدارية والمالية</h2>
                    <p class="text-muted">إدارة الموارد البشرية والشؤون المالية</p>
                </div>
                <div class="alert alert-info">
                    <h5>قريباً...</h5>
                    <p>سيتم تطوير هذا القطاع في المرحلة التالية من المشروع.</p>
                </div>
            </div>

            <div id="reports-page" class="page-content p-4" style="display: none;">
                <div class="page-header p-4 mb-4">
                    <h2 class="text-primary fw-bold">التقارير</h2>
                    <p class="text-muted">عرض وإنشاء التقارير الإحصائية</p>
                </div>
                <div class="alert alert-info">
                    <h5>قريباً...</h5>
                    <p>سيتم تطوير نظام التقارير في المرحلة التالية من المشروع.</p>
                </div>
            </div>

            <div id="settings-page" class="page-content p-4" style="display: none;">
                <div class="page-header p-4 mb-4">
                    <h2 class="text-primary fw-bold">الإعدادات</h2>
                    <p class="text-muted">إعدادات النظام والمستخدمين</p>
                </div>
                <div class="alert alert-info">
                    <h5>قريباً...</h5>
                    <p>سيتم تطوير صفحة الإعدادات في المرحلة التالية من المشروع.</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Crop Modal -->
    <div class="modal fade" id="addCropModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة محصول جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addCropForm">
                        <div class="mb-3">
                            <label class="form-label">اسم المحصول</label>
                            <input type="text" class="form-control" id="cropName" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">نوع المحصول</label>
                            <select class="form-select" id="cropType" required>
                                <option value="">اختر النوع</option>
                                <option value="خضروات">خضروات</option>
                                <option value="فواكه">فواكه</option>
                                <option value="حبوب">حبوب</option>
                                <option value="بقوليات">بقوليات</option>
                                <option value="أعلاف">أعلاف</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">اسم المزرعة</label>
                            <input type="text" class="form-control" id="farmName" required>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">المساحة (هكتار)</label>
                                    <input type="number" class="form-control" id="cropArea" step="0.1" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">الإنتاج المتوقع (طن)</label>
                                    <input type="number" class="form-control" id="expectedProduction" step="0.1" required>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">تاريخ الزراعة</label>
                            <input type="date" class="form-control" id="plantingDate" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">الحالة</label>
                            <select class="form-select" id="cropStatus" required>
                                <option value="زراعة">زراعة</option>
                                <option value="نمو">نمو</option>
                                <option value="إزهار">إزهار</option>
                                <option value="نضج">نضج</option>
                                <option value="حصاد">حصاد</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-success" onclick="addCrop()">حفظ المحصول</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Palm Farm Modal -->
    <div class="modal fade" id="addPalmFarmModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-success text-white">
                    <h5 class="modal-title">🌴 إضافة مزرعة نخيل جديدة</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addPalmFarmForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">اسم المزرعة</label>
                                    <input type="text" class="form-control" id="palmFarmName" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">المنطقة</label>
                                    <select class="form-select" id="palmRegion" required>
                                        <option value="">اختر المنطقة</option>
                                        <option value="الداخلية">الداخلية</option>
                                        <option value="الشرقية">الشرقية</option>
                                        <option value="الظاهرة">الظاهرة</option>
                                        <option value="البريمي">البريمي</option>
                                        <option value="الباطنة">الباطنة</option>
                                        <option value="مسندم">مسندم</option>
                                        <option value="ظفار">ظفار</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">عدد الأشجار الإجمالي</label>
                                    <input type="number" class="form-control" id="totalTrees" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">عدد الأشجار المنتجة</label>
                                    <input type="number" class="form-control" id="productiveTrees" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">الإنتاج المتوقع (طن)</label>
                                    <input type="number" class="form-control" id="palmProduction" step="0.1" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">نوع التمر</label>
                                    <select class="form-select" id="dateType" required>
                                        <option value="">اختر النوع</option>
                                        <option value="خلاص">خلاص</option>
                                        <option value="فرض">فرض</option>
                                        <option value="خصاب">خصاب</option>
                                        <option value="مبروم">مبروم</option>
                                        <option value="زهدي">زهدي</option>
                                        <option value="برحي">برحي</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">الحالة العامة</label>
                            <select class="form-select" id="palmStatus" required>
                                <option value="ممتاز">ممتاز</option>
                                <option value="جيد جداً">جيد جداً</option>
                                <option value="جيد">جيد</option>
                                <option value="متوسط">متوسط</option>
                                <option value="يحتاج رعاية">يحتاج رعاية</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-success" onclick="addPalmFarm()">حفظ البيانات</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Seedling Distribution Modal -->
    <div class="modal fade" id="addSeedlingModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-success text-white">
                    <h5 class="modal-title">🌱 تسجيل توزيع فسائل جديدة</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addSeedlingForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">تاريخ التوزيع</label>
                                    <input type="date" class="form-control" id="distributionDate" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">اسم المستفيد</label>
                                    <input type="text" class="form-control" id="beneficiaryName" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">المنطقة</label>
                                    <select class="form-select" id="seedlingRegion" required>
                                        <option value="">اختر المنطقة</option>
                                        <option value="الداخلية">الداخلية</option>
                                        <option value="الشرقية">الشرقية</option>
                                        <option value="الظاهرة">الظاهرة</option>
                                        <option value="البريمي">البريمي</option>
                                        <option value="الباطنة">الباطنة</option>
                                        <option value="مسندم">مسندم</option>
                                        <option value="ظفار">ظفار</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">عدد الفسائل</label>
                                    <input type="number" class="form-control" id="seedlingCount" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">نوع الفسائل</label>
                                    <select class="form-select" id="seedlingType" required>
                                        <option value="">اختر النوع</option>
                                        <option value="خلاص">خلاص</option>
                                        <option value="فرض">فرض</option>
                                        <option value="خصاب">خصاب</option>
                                        <option value="مبروم">مبروم</option>
                                        <option value="زهدي">زهدي</option>
                                        <option value="برحي">برحي</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">حالة النمو</label>
                                    <select class="form-select" id="growthStatus" required>
                                        <option value="ممتاز">ممتاز</option>
                                        <option value="جيد">جيد</option>
                                        <option value="متوسط">متوسط</option>
                                        <option value="يحتاج متابعة">يحتاج متابعة</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">ملاحظات</label>
                            <textarea class="form-control" id="seedlingNotes" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-success" onclick="addSeedling()">حفظ البيانات</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Sample data
        let crops = [
            {
                id: 1,
                name: 'طماطم الصوب',
                type: 'خضروات',
                farmName: 'مزرعة الخير',
                area: 25.5,
                expectedProduction: 180.0,
                plantingDate: '2024-06-06',
                status: 'نمو'
            },
            {
                id: 2,
                name: 'خيار هجين',
                type: 'خضروات',
                farmName: 'مزرعة النماء',
                area: 18.2,
                expectedProduction: 120.0,
                plantingDate: '2024-06-01',
                status: 'إزهار'
            },
            {
                id: 3,
                name: 'باذنجان أسود',
                type: 'خضروات',
                farmName: 'مزرعة الأمل',
                area: 22.8,
                expectedProduction: 150.0,
                plantingDate: '2024-05-27',
                status: 'نضج'
            },
            {
                id: 4,
                name: 'فلفل حلو',
                type: 'خضروات',
                farmName: 'مزرعة الخضار',
                area: 15.5,
                expectedProduction: 95.0,
                plantingDate: '2024-05-22',
                status: 'حصاد'
            },
            {
                id: 5,
                name: 'بصل أحمر',
                type: 'خضروات',
                farmName: 'مزرعة البصل',
                area: 30.0,
                expectedProduction: 200.0,
                plantingDate: '2024-05-15',
                status: 'نمو'
            }
        ];

        // Sample data for palm farms
        let palmFarms = [
            { id: 1, name: 'مزرعة الواحة الخضراء', region: 'الداخلية', totalTrees: 150, productiveTrees: 120, production: 25.5, dateType: 'خلاص', status: 'ممتاز' },
            { id: 2, name: 'مزرعة النخيل الذهبي', region: 'الشرقية', totalTrees: 200, productiveTrees: 180, production: 42.0, dateType: 'فرض', status: 'ممتاز' },
            { id: 3, name: 'مزرعة التمور الملكية', region: 'الظاهرة', totalTrees: 95, productiveTrees: 75, production: 18.2, dateType: 'خصاب', status: 'جيد' },
            { id: 4, name: 'مزرعة الأصيل', region: 'البريمي', totalTrees: 300, productiveTrees: 250, production: 65.8, dateType: 'مبروم', status: 'ممتاز' }
        ];

        // Sample data for seedlings
        let seedlings = [
            { id: 1, date: '2024/06/15', beneficiary: 'أحمد بن سالم', region: 'الداخلية', count: 25, type: 'خلاص', status: 'ممتاز', notes: 'نمو طبيعي' },
            { id: 2, date: '2024/06/10', beneficiary: 'فاطمة بنت محمد', region: 'الشرقية', count: 15, type: 'فرض', status: 'جيد', notes: 'يحتاج متابعة' },
            { id: 3, date: '2024/06/05', beneficiary: 'سعيد بن علي', region: 'الظاهرة', count: 30, type: 'خصاب', status: 'ممتاز', notes: 'نمو سريع' }
        ];

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            updateCurrentTime();
            setInterval(updateCurrentTime, 1000);

            // Login form handler
            document.getElementById('loginForm').addEventListener('submit', function(e) {
                e.preventDefault();
                login();
            });

            // Initialize charts when dashboard is shown
            setTimeout(() => {
                initializeCharts();
            }, 1000);
        });

        function updateCurrentTime() {
            const now = new Date();
            const timeString = now.toLocaleString('ar-SA', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            const timeElement = document.getElementById('currentTime');
            if (timeElement) {
                timeElement.textContent = `آخر تحديث: ${timeString}`;
            }
        }

        function login() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            if (username === 'admin' && password === 'admin') {
                document.getElementById('loginScreen').style.display = 'none';
                document.getElementById('mainApp').style.display = 'block';
                showPage('dashboard');

                // Show success message
                setTimeout(() => {
                    showNotification('تم تسجيل الدخول بنجاح!', 'success');
                }, 500);
            } else {
                showNotification('اسم المستخدم أو كلمة المرور غير صحيحة', 'error');
            }
        }

        function logout() {
            if (confirm('هل تريد تسجيل الخروج؟')) {
                document.getElementById('mainApp').style.display = 'none';
                document.getElementById('loginScreen').style.display = 'flex';

                // Reset form
                document.getElementById('loginForm').reset();
                document.getElementById('username').value = 'admin';
                document.getElementById('password').value = 'admin';
            }
        }

        function showPage(pageId) {
            // Hide all pages
            const pages = document.querySelectorAll('.page-content');
            pages.forEach(page => page.style.display = 'none');

            // Show selected page
            const targetPage = document.getElementById(pageId + '-page');
            if (targetPage) {
                targetPage.style.display = 'block';

                // Update active nav link
                const navLinks = document.querySelectorAll('.nav-link');
                navLinks.forEach(link => link.classList.remove('active'));

                const activeLink = document.querySelector(`[onclick="showPage('${pageId}')"]`);
                if (activeLink) {
                    activeLink.classList.add('active');
                }

                // Load page-specific content
                if (pageId === 'plant-sector') {
                    showPlantSection('palm-programs'); // Show default section
                } else if (pageId === 'dashboard') {
                    setTimeout(() => {
                        initializeCharts();
                    }, 100);
                }
            }
        }

        function showPlantSection(sectionId) {
            // Hide all plant sections
            const sections = document.querySelectorAll('.plant-section-content');
            sections.forEach(section => section.style.display = 'none');

            // Show selected section
            const targetSection = document.getElementById(sectionId);
            if (targetSection) {
                targetSection.style.display = 'block';
            }

            // Update active tab
            const tabs = document.querySelectorAll('.sector-tab');
            tabs.forEach(tab => tab.classList.remove('active'));

            const activeTab = document.querySelector(`[onclick="showPlantSection('${sectionId}')"]`);
            if (activeTab) {
                activeTab.classList.add('active');
            }

            // Load section-specific content
            if (sectionId === 'vegetable-crops') {
                loadCropsTable();
            } else if (sectionId === 'palm-programs') {
                // Show default palm sub-section (seedlings)
                setTimeout(() => {
                    showPalmSubSection('palm-seedlings');
                }, 100);
            } else if (sectionId === 'fruit-seedlings') {
                // Show default fruit sub-section (fields)
                setTimeout(() => {
                    showFruitSubSection('fruit-fields');
                }, 100);
            } else if (sectionId === 'guidance-fields') {
                // Show default guidance sub-section (wheat-crop)
                setTimeout(() => {
                    showGuidanceSubSection('wheat-crop');
                }, 100);
            }
        }

        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.getElementById('mainContent');

            sidebar.classList.toggle('show');
            mainContent.classList.toggle('expanded');
        }

        function loadCropsTable() {
            const tbody = document.getElementById('cropsTableBody');
            if (!tbody) return;

            tbody.innerHTML = '';

            crops.forEach(crop => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>
                        <div class="d-flex align-items-center">
                            <span class="material-icons text-success me-2">eco</span>
                            <div>
                                <strong>${crop.name}</strong><br>
                                <small class="text-muted">${crop.farmName}</small>
                            </div>
                        </div>
                    </td>
                    <td><span class="badge bg-success">${crop.type}</span></td>
                    <td>${crop.area}</td>
                    <td>${crop.expectedProduction}</td>
                    <td>${crop.plantingDate}</td>
                    <td><span class="badge ${getStatusBadgeClass(crop.status)}">${crop.status}</span></td>
                    <td>
                        <button class="btn btn-sm btn-outline-info me-1" onclick="viewCrop(${crop.id})">
                            <span class="material-icons">visibility</span>
                        </button>
                        <button class="btn btn-sm btn-outline-warning me-1" onclick="editCrop(${crop.id})">
                            <span class="material-icons">edit</span>
                        </button>
                        <button class="btn btn-sm btn-outline-danger" onclick="deleteCrop(${crop.id})">
                            <span class="material-icons">delete</span>
                        </button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        function getStatusBadgeClass(status) {
            switch (status) {
                case 'زراعة': return 'bg-info';
                case 'نمو': return 'bg-primary';
                case 'إزهار': return 'bg-warning';
                case 'نضج': return 'bg-success';
                case 'حصاد': return 'bg-secondary';
                case 'ممتاز': return 'bg-success';
                case 'جيد جداً': return 'bg-info';
                case 'جيد': return 'bg-warning';
                case 'متوسط': return 'bg-secondary';
                default: return 'bg-secondary';
            }
        }

        function loadPalmFarmsTable() {
            // Update the static table with dynamic data
            const tableBody = document.querySelector('#palm-programs tbody');
            if (!tableBody) return;

            tableBody.innerHTML = '';

            palmFarms.forEach(farm => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${farm.name}</td>
                    <td>${farm.region}</td>
                    <td>${farm.totalTrees}</td>
                    <td>${farm.productiveTrees}</td>
                    <td>${farm.production}</td>
                    <td>${farm.dateType}</td>
                    <td><span class="badge ${getStatusBadgeClass(farm.status)}">${farm.status}</span></td>
                    <td>
                        <button class="btn btn-sm btn-outline-info me-1" onclick="viewPalmFarm(${farm.id})" title="عرض">
                            <span class="material-icons">visibility</span>
                        </button>
                        <button class="btn btn-sm btn-outline-warning me-1" onclick="editPalmFarm(${farm.id})" title="تعديل">
                            <span class="material-icons">edit</span>
                        </button>
                        <button class="btn btn-sm btn-outline-danger" onclick="deletePalmFarm(${farm.id})" title="حذف">
                            <span class="material-icons">delete</span>
                        </button>
                    </td>
                `;
                tableBody.appendChild(row);
            });
        }

        function loadSeedlingsTable() {
            // Update the static table with dynamic data
            const tableBody = document.querySelector('#palm-seedlings tbody');
            if (!tableBody) return;

            tableBody.innerHTML = '';

            seedlings.forEach(seedling => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${seedling.date}</td>
                    <td>${seedling.beneficiary}</td>
                    <td>${seedling.region}</td>
                    <td>${seedling.count}</td>
                    <td>${seedling.type}</td>
                    <td><span class="badge ${getStatusBadgeClass(seedling.status)}">${seedling.status}</span></td>
                    <td>${seedling.notes}</td>
                    <td>
                        <button class="btn btn-sm btn-outline-info me-1" onclick="viewSeedling(${seedling.id})" title="عرض">
                            <span class="material-icons">visibility</span>
                        </button>
                        <button class="btn btn-sm btn-outline-warning me-1" onclick="editSeedling(${seedling.id})" title="تعديل">
                            <span class="material-icons">edit</span>
                        </button>
                        <button class="btn btn-sm btn-outline-danger" onclick="deleteSeedling(${seedling.id})" title="حذف">
                            <span class="material-icons">delete</span>
                        </button>
                    </td>
                `;
                tableBody.appendChild(row);
            });
        }

        function showAddCropModal() {
            const modal = new bootstrap.Modal(document.getElementById('addCropModal'));
            modal.show();
        }

        function addCrop() {
            const form = document.getElementById('addCropForm');
            const formData = new FormData(form);

            const newCrop = {
                id: crops.length + 1,
                name: document.getElementById('cropName').value,
                type: document.getElementById('cropType').value,
                farmName: document.getElementById('farmName').value,
                area: parseFloat(document.getElementById('cropArea').value),
                expectedProduction: parseFloat(document.getElementById('expectedProduction').value),
                plantingDate: document.getElementById('plantingDate').value,
                status: document.getElementById('cropStatus').value
            };

            crops.push(newCrop);
            loadCropsTable();

            // Close modal and reset form
            const modal = bootstrap.Modal.getInstance(document.getElementById('addCropModal'));
            modal.hide();
            form.reset();

            showNotification('تم إضافة المحصول بنجاح!', 'success');
        }

        // Palm Farm Functions
        function showAddPalmFarmModal() {
            const modal = new bootstrap.Modal(document.getElementById('addPalmFarmModal'));
            modal.show();
        }

        function addPalmFarm() {
            const form = document.getElementById('addPalmFarmForm');

            // Get form data
            const newFarm = {
                id: palmFarms.length + 1,
                name: document.getElementById('palmFarmName').value,
                region: document.getElementById('palmRegion').value,
                totalTrees: parseInt(document.getElementById('totalTrees').value),
                productiveTrees: parseInt(document.getElementById('productiveTrees').value),
                production: parseFloat(document.getElementById('palmProduction').value),
                dateType: document.getElementById('dateType').value,
                status: document.getElementById('palmStatus').value
            };

            // Add to array
            palmFarms.push(newFarm);

            // Update the table
            loadPalmFarmsTable();

            // Close modal and reset form
            const modal = bootstrap.Modal.getInstance(document.getElementById('addPalmFarmModal'));
            modal.hide();
            form.reset();

            showNotification('تم إضافة مزرعة النخيل بنجاح!', 'success');
        }

        // Seedling Functions
        function showAddSeedlingModal() {
            const modal = new bootstrap.Modal(document.getElementById('addSeedlingModal'));
            modal.show();
        }

        function addSeedling() {
            const form = document.getElementById('addSeedlingForm');

            // Get form data
            const newSeedling = {
                id: seedlings.length + 1,
                date: document.getElementById('distributionDate').value,
                beneficiary: document.getElementById('beneficiaryName').value,
                region: document.getElementById('seedlingRegion').value,
                count: parseInt(document.getElementById('seedlingCount').value),
                type: document.getElementById('seedlingType').value,
                status: document.getElementById('growthStatus').value,
                notes: document.getElementById('seedlingNotes').value
            };

            // Add to array
            seedlings.push(newSeedling);

            // Update the table
            loadSeedlingsTable();

            // Close modal and reset form
            const modal = bootstrap.Modal.getInstance(document.getElementById('addSeedlingModal'));
            modal.hide();
            form.reset();

            showNotification('تم تسجيل توزيع الفسائل بنجاح!', 'success');
        }

        // Field Crop Functions
        function showAddFieldCropModal() {
            showNotification('سيتم إضافة نموذج المحاصيل الحقلية قريباً', 'info');
        }

        // Fruit Tree Functions
        function showAddFruitTreeModal() {
            showNotification('سيتم إضافة نموذج أشجار الفاكهة قريباً', 'info');
        }

        // Ornamental Plants Functions
        function showAddOrnamentalModal() {
            showNotification('سيتم إضافة نموذج النباتات الزينة قريباً', 'info');
        }

        // Medicinal Plants Functions
        function showAddMedicinalModal() {
            showNotification('سيتم إضافة نموذج النباتات الطبية قريباً', 'info');
        }

        // Greenhouse Functions
        function showAddGreenhouseModal() {
            showNotification('سيتم إضافة نموذج البيوت المحمية قريباً', 'info');
        }

        // Irrigation Functions
        function showAddIrrigationModal() {
            showNotification('سيتم إضافة نموذج أنظمة الري قريباً', 'info');
        }

        // Fertilizer Functions
        function showAddFertilizerModal() {
            showNotification('سيتم إضافة نموذج الأسمدة والمبيدات قريباً', 'info');
        }

        // Disease Functions
        function showAddDiseaseModal() {
            showNotification('سيتم إضافة نموذج الآفات والأمراض قريباً', 'info');
        }

        // Research Functions
        function showAddResearchModal() {
            showNotification('سيتم إضافة نموذج البحوث الزراعية قريباً', 'info');
        }

        // Palm Sub-sections Functions
        function showPalmSubSection(subSectionId) {
            // Hide all palm sub-contents
            document.querySelectorAll('.palm-sub-content').forEach(content => {
                content.style.display = 'none';
            });

            // Remove active class from all palm sub-tabs
            document.querySelectorAll('.palm-sub-tab').forEach(tab => {
                tab.classList.remove('active');
                tab.classList.remove('btn-primary', 'btn-warning');
                if (subSectionId === 'palm-seedlings') {
                    tab.classList.add('btn-outline-primary');
                } else {
                    tab.classList.add('btn-outline-warning');
                }
            });

            // Show selected content
            const contentElement = document.getElementById(subSectionId + '-content');
            if (contentElement) {
                contentElement.style.display = 'block';
            }

            // Add active class to clicked tab
            event.target.closest('button').classList.add('active');
            if (subSectionId === 'palm-seedlings') {
                event.target.closest('button').classList.remove('btn-outline-primary');
                event.target.closest('button').classList.add('btn-primary');
            } else {
                event.target.closest('button').classList.remove('btn-outline-warning');
                event.target.closest('button').classList.add('btn-warning');
            }

            // Load specific data
            if (subSectionId === 'palm-seedlings') {
                loadSeedlingsTable();
            }
        }

        // Fruit Sub-sections Functions
        function showFruitSubSection(subSectionId) {
            // Hide all fruit sub-contents
            document.querySelectorAll('.fruit-sub-content').forEach(content => {
                content.style.display = 'none';
            });

            // Remove active class from all fruit sub-tabs
            document.querySelectorAll('.fruit-sub-tab').forEach(tab => {
                tab.classList.remove('active');
                tab.classList.remove('btn-success', 'btn-info');
                if (subSectionId === 'fruit-fields') {
                    tab.classList.add('btn-outline-success');
                } else {
                    tab.classList.add('btn-outline-info');
                }
            });

            // Show selected content
            const contentElement = document.getElementById(subSectionId + '-content');
            if (contentElement) {
                contentElement.style.display = 'block';
            }

            // Add active class to clicked tab
            event.target.closest('button').classList.add('active');
            if (subSectionId === 'fruit-fields') {
                event.target.closest('button').classList.remove('btn-outline-success');
                event.target.closest('button').classList.add('btn-success');
            } else {
                event.target.closest('button').classList.remove('btn-outline-info');
                event.target.closest('button').classList.add('btn-info');
            }
        }

        // Guidance Fields Sub-sections Functions
        function showGuidanceSubSection(subSectionId) {
            // Hide all guidance sub-contents
            document.querySelectorAll('.guidance-sub-content').forEach(content => {
                content.style.display = 'none';
            });

            // Remove active class from all guidance sub-tabs
            document.querySelectorAll('.guidance-sub-tab').forEach(tab => {
                tab.classList.remove('active');
                tab.classList.remove('btn-success', 'btn-primary', 'btn-info');
                if (subSectionId === 'wheat-crop') {
                    tab.classList.add('btn-outline-success');
                } else if (subSectionId === 'participating-farmers') {
                    tab.classList.add('btn-outline-primary');
                } else {
                    tab.classList.add('btn-outline-info');
                }
            });

            // Show selected content
            const contentElement = document.getElementById(subSectionId + '-content');
            if (contentElement) {
                contentElement.style.display = 'block';
            }

            // Add active class to clicked tab
            event.target.closest('button').classList.add('active');
            if (subSectionId === 'wheat-crop') {
                event.target.closest('button').classList.remove('btn-outline-success');
                event.target.closest('button').classList.add('btn-success');
            } else if (subSectionId === 'participating-farmers') {
                event.target.closest('button').classList.remove('btn-outline-primary');
                event.target.closest('button').classList.add('btn-primary');
            } else {
                event.target.closest('button').classList.remove('btn-outline-info');
                event.target.closest('button').classList.add('btn-info');
            }
        }

        // Wilayat Filter Function
        function filterByWilayat(wilayat) {
            const table = document.getElementById('farmers-table');
            const rows = table.querySelectorAll('tbody tr');

            // Update filter buttons
            document.querySelectorAll('.wilayat-filter').forEach(btn => {
                btn.classList.remove('active', 'btn-info');
                btn.classList.add('btn-outline-secondary');
            });

            event.target.classList.remove('btn-outline-secondary');
            event.target.classList.add('btn-info', 'active');

            // Filter rows
            rows.forEach(row => {
                if (wilayat === 'all' || row.getAttribute('data-wilayat') === wilayat) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        }

        function viewCrop(id) {
            const crop = crops.find(c => c.id === id);
            if (crop) {
                alert(`تفاصيل المحصول:\nالاسم: ${crop.name}\nالنوع: ${crop.type}\nالمزرعة: ${crop.farmName}\nالمساحة: ${crop.area} هكتار\nالإنتاج المتوقع: ${crop.expectedProduction} طن`);
            }
        }

        function editCrop(id) {
            showNotification('سيتم تطوير ميزة التعديل قريباً', 'info');
        }

        function deleteCrop(id) {
            if (confirm('هل تريد حذف هذا المحصول؟')) {
                crops = crops.filter(c => c.id !== id);
                loadCropsTable();
                showNotification('تم حذف المحصول بنجاح!', 'success');
            }
        }

        // Palm Farm CRUD Functions
        function viewPalmFarm(id) {
            const farm = palmFarms.find(f => f.id === id);
            if (farm) {
                alert(`تفاصيل مزرعة النخيل:\nالاسم: ${farm.name}\nالمنطقة: ${farm.region}\nعدد الأشجار: ${farm.totalTrees}\nالأشجار المنتجة: ${farm.productiveTrees}\nالإنتاج: ${farm.production} طن\nنوع التمر: ${farm.dateType}\nالحالة: ${farm.status}`);
            }
        }

        function editPalmFarm(id) {
            showNotification('سيتم تطوير ميزة التعديل قريباً', 'info');
        }

        function deletePalmFarm(id) {
            if (confirm('هل تريد حذف هذه المزرعة؟')) {
                palmFarms = palmFarms.filter(f => f.id !== id);
                loadPalmFarmsTable();
                showNotification('تم حذف المزرعة بنجاح!', 'success');
            }
        }

        // Seedling CRUD Functions
        function viewSeedling(id) {
            const seedling = seedlings.find(s => s.id === id);
            if (seedling) {
                alert(`تفاصيل توزيع الفسائل:\nالتاريخ: ${seedling.date}\nالمستفيد: ${seedling.beneficiary}\nالمنطقة: ${seedling.region}\nالعدد: ${seedling.count}\nالنوع: ${seedling.type}\nالحالة: ${seedling.status}\nالملاحظات: ${seedling.notes}`);
            }
        }

        function editSeedling(id) {
            showNotification('سيتم تطوير ميزة التعديل قريباً', 'info');
        }

        function deleteSeedling(id) {
            if (confirm('هل تريد حذف هذا السجل؟')) {
                seedlings = seedlings.filter(s => s.id !== id);
                loadSeedlingsTable();
                showNotification('تم حذف السجل بنجاح!', 'success');
            }
        }

        function initializeCharts() {
            const ctx = document.getElementById('productionChart');
            if (!ctx) return;

            // Destroy existing chart if it exists
            if (window.productionChart) {
                window.productionChart.destroy();
            }

            window.productionChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
                    datasets: [{
                        label: 'القطاع النباتي',
                        data: [1200, 1350, 1500, 1650, 1800, 1680],
                        borderColor: '#4caf50',
                        backgroundColor: 'rgba(76, 175, 80, 0.1)',
                        tension: 0.4
                    }, {
                        label: 'القطاع الحيواني',
                        data: [800, 850, 900, 950, 1000, 980],
                        borderColor: '#ff9800',
                        backgroundColor: 'rgba(255, 152, 0, 0.1)',
                        tension: 0.4
                    }, {
                        label: 'قطاع المياه',
                        data: [400, 420, 450, 480, 500, 490],
                        borderColor: '#2196f3',
                        backgroundColor: 'rgba(33, 150, 243, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                        },
                        title: {
                            display: true,
                            text: 'الإنتاج الشهري (طن)'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        function showNotification(message, type) {
            const alertClass = type === 'success' ? 'alert-success' :
                              type === 'error' ? 'alert-danger' :
                              type === 'info' ? 'alert-info' : 'alert-warning';

            const notification = document.createElement('div');
            notification.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
            notification.style.cssText = 'top: 20px; left: 20px; z-index: 9999; min-width: 300px;';
            notification.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 5000);
        }

        // Animate statistics on page load
        function animateStats() {
            const stats = document.querySelectorAll('.stat-card h3');
            stats.forEach((stat, index) => {
                const finalValue = parseInt(stat.textContent.replace(/,/g, ''));
                let currentValue = 0;
                const increment = finalValue / 50;

                const timer = setInterval(() => {
                    currentValue += increment;
                    if (currentValue >= finalValue) {
                        currentValue = finalValue;
                        clearInterval(timer);
                    }
                    stat.textContent = Math.floor(currentValue).toLocaleString('ar-SA');
                }, 30);
            });
        }

        // Call animate stats when dashboard is shown
        setTimeout(animateStats, 1000);
    </script>
</body>
</html>